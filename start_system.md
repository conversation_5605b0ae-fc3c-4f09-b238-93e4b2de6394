# YOLO目标检测系统启动指南

## 系统概述

这是一个基于WebSocket的实时YOLO目标检测系统，包含：
- **后端**: FastAPI + WebSocket + YOLOv8
- **前端**: 原生HTML/CSS/JavaScript + Canvas绘图

## 功能特性

✅ 图片选择（支持拖拽上传）  
✅ 实时WebSocket通信  
✅ YOLOv8目标检测  
✅ 检测结果可视化（边界框 + 标签）  
✅ 交互式检测框高亮  
✅ 响应式UI设计  
✅ 连接状态监控  
✅ 自动重连机制  

## 启动步骤

### 1. 环境配置和依赖安装

系统支持两种Python环境配置方式：

**方式一：使用conda环境（推荐）**
```bash
# 一键启动会自动检测并配置conda环境
./start.sh

# 手动配置conda环境
conda create -n pallet_vision python=3.11 -c conda-forge --override-channels -y
conda activate pallet_vision
cd backend
pip install -r requirements.txt
```

**方式二：使用系统Python环境**
```bash
cd backend
pip install -r requirements.txt
```

> **说明**: 启动脚本会自动检测是否安装了conda，如果检测到conda会优先使用名为`pallet_vision`的虚拟环境。如果环境不存在会自动创建。

### 2. 启动后端服务

```bash
cd backend
python app.py
```

后端服务将在 `http://localhost:8000` 启动

- WebSocket端点: `ws://localhost:8000/ws`
- API文档: `http://localhost:8000/docs`
- 健康检查: `http://localhost:8000/health`

### 3. 启动前端服务

**方法一：使用Python内置服务器**
```bash
cd frontend
python -m http.server 8080 --directory public
```

**方法二：使用Node.js http-server**
```bash
cd frontend
npm install
npm run serve
```

前端将在 `http://localhost:8080` 启动

### 4. 使用系统

1. 打开浏览器访问 `http://localhost:8080`
2. 等待WebSocket连接成功（连接状态显示为绿色）
3. 选择或拖拽图片到上传区域
4. 点击"开始检测"按钮
5. 查看检测结果和可视化边界框

## 系统架构

```
前端 (localhost:8080)
    ↓ WebSocket
后端 (localhost:8000)
    ↓ 调用
YOLO模型 (./backend/core_engine/weights/yolov8n.pt)
```

## 检测结果格式

检测成功后，系统会返回：
- 边界框坐标 `[x1, y1, x2, y2]`
- 类别名称和ID
- 置信度分数
- 可视化标注

## 支持的图片格式

- JPG/JPEG
- PNG
- 其他浏览器支持的图片格式

## 故障排除

### WebSocket连接失败
- 确保后端服务正在运行
- 检查防火墙设置
- 查看浏览器控制台错误信息

### YOLO模型加载失败
- 确认模型文件存在: `backend/core_engine/weights/yolov8n.pt`
- 检查Python依赖是否正确安装
- 查看后端控制台日志

### 图片上传失败
- 确认图片格式正确
- 检查图片文件大小（建议<10MB）
- 查看浏览器网络标签页

## 技术栈

**后端:**
- FastAPI - Web框架
- WebSocket - 实时通信
- Ultralytics YOLOv8 - 目标检测
- PyTorch - 深度学习框架
- Pillow - 图像处理

**前端:**
- HTML5 Canvas - 图像显示和标注
- WebSocket API - 实时通信
- 原生JavaScript - 无框架依赖
- CSS3 - 现代化UI设计

## 扩展功能

可以进一步扩展的功能：
- 支持视频流检测
- 自定义YOLO模型
- 批量图片处理
- 检测结果导出
- 用户认证系统 