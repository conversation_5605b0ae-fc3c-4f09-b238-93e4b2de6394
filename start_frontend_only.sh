#!/bin/bash

# 仅启动前端服务的脚本（用于测试）

echo "🚀 启动托盘状态检测系统前端..."
echo "================================"

# 检查新前端架构文件是否存在
if [ ! -f "frontend_reference/public/index.html" ]; then
    echo "❌ 错误: 新前端架构文件不存在"
    exit 1
fi

cd frontend_reference

# 确保 public 目录中有最新的文件
echo "📁 同步文件到 public 目录..."
mkdir -p public/styles public/services public/components

# 复制样式文件
cp src/styles/* public/styles/ 2>/dev/null || true

# 复制服务文件
cp src/services/* public/services/ 2>/dev/null || true

# 复制组件文件
cp src/components/* public/components/ 2>/dev/null || true

# 复制主要 JS 文件
cp src/App.js public/ 2>/dev/null || true
cp src/index.js public/ 2>/dev/null || true

echo "✅ 文件同步完成"

# 获取本机IP地址
LOCAL_IP=$(ifconfig | grep -E "inet (10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)" | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
if [ -z "$LOCAL_IP" ]; then
    # 备用方法获取IP
    LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "未获取到IP")
fi

echo ""
echo "🌐 启动前端服务..."
echo "前端地址: http://localhost:8080"
echo ""

# 启动前端服务（从public目录启动，绑定到所有网络接口）
echo "✅ 前端服务启动成功"
echo ""
echo "🎉 前端启动成功！"
echo "================================"
echo "📱 本机访问地址: http://localhost:8080"
echo "🌐 局域网访问地址: http://${LOCAL_IP}:8080"
echo ""
echo "💡 使用说明:"
echo "  📱 本机访问: 打开浏览器访问 http://localhost:8080"
echo "  🌐 局域网访问: 其他设备打开浏览器访问 http://${LOCAL_IP}:8080"
echo "  ⚠️  注意: 后端服务未启动，连接状态会显示断开"
echo "  📷 可以测试界面功能，但无法进行真实检测"
echo ""
echo "🛑 按 Ctrl+C 停止服务"
echo ""

# 启动HTTP服务器
python3 -m http.server 8080 --directory public --bind 0.0.0.0
