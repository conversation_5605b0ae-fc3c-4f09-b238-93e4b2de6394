#!/bin/bash

# YOLO目标检测系统 - 环境配置脚本

echo "🐍 YOLO检测系统环境配置"
echo "================================"

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: conda 未安装"
    echo ""
    echo "请先安装conda或miniconda:"
    echo "- Anaconda: https://www.anaconda.com/products/distribution"
    echo "- Miniconda: https://docs.conda.io/en/latest/miniconda.html"
    echo ""
    echo "或者使用系统Python环境运行项目:"
    echo "  cd backend && pip install -r requirements.txt"
    exit 1
fi

echo "✅ 检测到conda环境"

# 初始化conda
eval "$(conda shell.bash hook)"

# 检查pallet_vision环境是否存在
if conda env list | grep -q "pallet_vision"; then
    echo "⚠️  pallet_vision 环境已存在"
    echo ""
    read -p "是否重新创建环境? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️  删除现有环境..."
        conda env remove -n pallet_vision -y
        echo "✅ 环境删除完成"
    else
        echo "📦 使用现有环境"
        conda activate pallet_vision
        echo "✅ 环境激活成功"
        echo ""
        echo "🔍 当前环境信息:"
        echo "Python版本: $(python --version)"
        echo "环境路径: $CONDA_PREFIX"
        echo ""
        echo "💡 要重新安装依赖，请运行:"
        echo "  cd backend && pip install -r requirements.txt"
        exit 0
    fi
fi

# 创建新环境
echo "📦 创建 pallet_vision conda环境..."
# 使用conda-forge避免Terms of Service问题，禁用默认channels
conda create -n pallet_vision python=3.11 -c conda-forge --override-channels -y

if [ $? -ne 0 ]; then
    echo "❌ 错误: 创建conda环境失败"
    echo ""
    echo "💡 可能的解决方案:"
    echo "1. 手动接受Terms of Service:"
    echo "   conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/main"
    echo "   conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/r"
    echo ""
    echo "2. 使用不同的conda channel:"
    echo "   conda create -n pallet_vision python=3.11 -c conda-forge --override-channels -y"
    echo ""
    echo "3. 检查conda配置:"
    echo "   conda config --show channels"
    exit 1
fi

echo "✅ 环境创建成功"

# 激活环境
echo "🔄 激活环境..."
conda activate pallet_vision

if [ $? -ne 0 ]; then
    echo "❌ 错误: 激活环境失败"
    exit 1
fi

echo "✅ 环境激活成功"

# 检查requirements.txt是否存在
if [ ! -f "backend/requirements.txt" ]; then
    echo "❌ 错误: backend/requirements.txt 不存在"
    echo "请确保在项目根目录运行此脚本"
    exit 1
fi

# 安装依赖
echo "📦 安装Python依赖包..."
cd backend
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ 错误: 安装依赖失败"
    exit 1
fi

cd ..

echo ""
echo "🎉 环境配置完成!"
echo "================================"
echo "🐍 环境名称: pallet_vision"
echo "🔢 Python版本: $(python --version)"
echo "📁 环境路径: $CONDA_PREFIX"
echo ""
echo "📋 使用说明:"
echo "1. 激活环境: conda activate pallet_vision"
echo "2. 启动系统: ./start.sh"
echo "3. 测试检测: cd backend && python test_detection.py"
echo "4. 退出环境: conda deactivate"
echo ""
echo "💡 提示: 启动脚本会自动检测并使用此环境" 