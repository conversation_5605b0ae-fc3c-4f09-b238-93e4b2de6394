# 日志文件
*.log
logs/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.yolo/
yolo-env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (如果有前端项目)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 模型文件 (通常很大)
*.pt
*.pth
*.h5
*.pkl
*.model

# 数据文件
data/
datasets/
*.csv
*.json
!package.json
!config.json

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 缓存
.cache/
.pytest_cache/

# 环境配置
.env.local
.env.development
.env.test
.env.production
