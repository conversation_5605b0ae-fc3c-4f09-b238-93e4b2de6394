#!/bin/bash

# Conda Terms of Service 修复脚本

echo "🔧 Conda Terms of Service 修复工具"
echo "================================"

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: conda 未安装"
    exit 1
fi

echo "✅ 检测到conda环境"

# 初始化conda
eval "$(conda shell.bash hook)"

echo ""
echo "🛠️  修复选项:"
echo "1. 接受Anaconda Terms of Service (推荐)"
echo "2. 配置使用conda-forge channel"
echo "3. 移除problematic channels"
echo "4. 显示当前channel配置"
echo ""

read -p "选择修复方案 (1-4): " choice

case $choice in
    1)
        echo "📝 接受Anaconda Terms of Service..."
        conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/main
        conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/r
        
        if [ $? -eq 0 ]; then
            echo "✅ Terms of Service已接受"
        else
            echo "❌ 接受ToS失败"
        fi
        ;;
    2)
        echo "🔧 配置conda-forge channel..."
        conda config --add channels conda-forge
        conda config --set channel_priority strict
        
        echo "✅ conda-forge channel已配置"
        echo "现在可以使用: conda create -n pallet_vision python=3.11 -c conda-forge --override-channels -y"
        ;;
    3)
        echo "🗑️  移除problematic channels..."
        conda config --remove channels https://repo.anaconda.com/pkgs/main 2>/dev/null
        conda config --remove channels https://repo.anaconda.com/pkgs/r 2>/dev/null
        conda config --remove channels defaults 2>/dev/null
        
        # 添加conda-forge作为主要channel
        conda config --add channels conda-forge
        
        echo "✅ 已移除problematic channels并配置conda-forge"
        ;;
    4)
        echo "📋 当前channel配置:"
        conda config --show channels
        echo ""
        echo "📋 当前tos状态:"
        conda tos list 2>/dev/null || echo "无法显示ToS状态"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "💡 修复完成后，可以尝试:"
echo "1. 运行 ./start.sh 启动系统"
echo "2. 运行 ./setup_env.sh 配置环境"
echo "3. 手动创建环境: conda create -n pallet_vision python=3.11 -c conda-forge --override-channels -y" 