# 🌐 局域网访问配置指南

## 概述

YOLO目标检测系统现已支持局域网访问，允许同一网络内的其他设备通过浏览器访问系统。

## 🚀 快速开始

### 1. 启动系统
```bash
./start.sh
```

启动后会自动显示本机IP地址和局域网访问地址。

### 2. 访问地址
- **本机访问**: `http://localhost:8080`
- **局域网访问**: `http://[本机IP]:8080`

例如：`http://*************:8080`

## 📱 设备兼容性

### 支持的设备类型
- ✅ **Windows电脑** - Chrome, Edge, Firefox
- ✅ **Mac电脑** - Safari, Chrome, Firefox
- ✅ **Android手机/平板** - Chrome, Firefox
- ✅ **iPhone/iPad** - Safari, Chrome
- ✅ **Linux电脑** - Chrome, Firefox

### 推荐浏览器
- Chrome 80+
- Safari 13+
- Firefox 75+
- Edge 80+

## 🔧 配置验证

### 检查网络配置
运行网络检测脚本：
```bash
./check_network.sh
```

该脚本会检查：
- 本机IP地址
- 端口占用情况
- 服务连接状态
- 防火墙设置

### 手动验证
1. **检查服务状态**：
   ```bash
   curl http://localhost:8080
   curl http://localhost:8000/health
   ```

2. **检查局域网连接**：
   ```bash
   curl http://[本机IP]:8080
   curl http://[本机IP]:8000/health
   ```

## 🛡️ 网络安全配置

### 防火墙设置

#### macOS
1. 打开"系统偏好设置" → "安全性与隐私" → "防火墙"
2. 如果防火墙已启用，点击"防火墙选项"
3. 允许"Python"接受传入连接

#### Windows
1. 打开"Windows Defender 防火墙"
2. 选择"允许应用或功能通过Windows Defender防火墙"
3. 添加Python.exe到允许列表

#### Linux (Ubuntu/Debian)
```bash
sudo ufw allow 8000
sudo ufw allow 8080
```

### 端口配置
系统使用以下端口：
- **8080**: 前端Web服务
- **8000**: 后端API和WebSocket服务

## 📋 故障排除

### 常见问题

#### 1. 无法从其他设备访问
**可能原因**：
- 防火墙阻止连接
- 设备不在同一网络
- 服务未正确绑定

**解决方案**：
```bash
# 检查网络配置
./check_network.sh

# 检查防火墙状态
sudo ufw status  # Linux
netsh advfirewall show allprofiles  # Windows

# 重启服务
./stop.sh && ./start.sh
```

#### 2. WebSocket连接失败
**现象**：页面显示"连接中..."或"连接已断开"

**解决方案**：
1. 确保后端服务正常运行
2. 检查8000端口是否被其他程序占用
3. 清除浏览器缓存后重新访问

#### 3. 图片上传失败
**可能原因**：
- 网络不稳定
- 图片文件过大
- 后端服务异常

**解决方案**：
- 使用较小的图片文件（建议<5MB）
- 检查后端日志：`tail -f backend.log`
- 重新启动服务

### 网络诊断命令

```bash
# 检查端口监听状态
lsof -i :8000
lsof -i :8080

# 检查网络连接
ping [本机IP]
telnet [本机IP] 8080

# 检查路由表
route -n  # Linux/macOS
route print  # Windows
```

## 🔗 高级配置

### 自定义端口
如需修改端口，编辑以下文件：

1. **前端端口** (start.sh):
   ```bash
   python3 -m http.server [新端口] --bind 0.0.0.0
   ```

2. **后端端口** (backend/app.py):
   ```python
   uvicorn.run("app:app", host="0.0.0.0", port=[新端口])
   ```

3. **WebSocket连接** (frontend/src/app.js):
   ```javascript
   const wsUrl = `ws://${hostname}:[新端口]/ws`;
   ```

### HTTPS配置
如需HTTPS访问，可使用nginx反向代理：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8080;
    }
    
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 📊 性能优化

### 局域网使用建议
- 使用5GHz WiFi以获得更好性能
- 确保路由器支持多设备并发
- 避免在网络高峰期使用
- 考虑使用有线连接获得最佳性能

### 移动设备优化
- 建议使用横屏模式获得更好体验
- 在移动数据网络下使用时注意流量消耗
- 关闭其他占用网络的应用

## 🆘 技术支持

如遇到无法解决的问题：
1. 运行 `./check_network.sh` 生成诊断报告
2. 查看日志文件：`backend.log` 和 `frontend.log`
3. 检查系统要求和兼容性

---

**注意**：局域网访问功能仅限同一网络内使用，如需互联网访问，请配置适当的网络安全设置。 