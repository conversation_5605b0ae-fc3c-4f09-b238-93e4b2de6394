<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 3px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>前端功能测试</h1>
    
    <div class="test-section">
        <h2>WebSocket 服务测试</h2>
        <button onclick="testWebSocketService()">测试 WebSocket 服务</button>
        <div id="websocket-result"></div>
    </div>

    <div class="test-section">
        <h2>组件初始化测试</h2>
        <button onclick="testComponentsInitialization()">测试组件初始化</button>
        <div id="components-result"></div>
    </div>

    <div class="test-section">
        <h2>模拟检测结果测试</h2>
        <button onclick="testMockDetection()">测试模拟检测</button>
        <div id="detection-result"></div>
    </div>

    <div class="test-section">
        <h2>连接状态测试</h2>
        <button onclick="testConnectionStatus()">测试连接状态</button>
        <div id="connection-result"></div>
    </div>

    <script>
        // 简化的测试函数
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function testWebSocketService() {
            const container = document.getElementById('websocket-result');
            container.innerHTML = '';
            
            try {
                // 测试 WebSocket 服务是否存在
                if (typeof WebSocketService !== 'undefined') {
                    addResult('websocket-result', '✓ WebSocketService 类已定义', 'success');
                    
                    // 创建实例测试
                    const ws = new WebSocketService();
                    addResult('websocket-result', '✓ WebSocketService 实例创建成功', 'success');
                    
                    // 测试方法是否存在
                    if (typeof ws.sendImageDetection === 'function') {
                        addResult('websocket-result', '✓ sendImageDetection 方法存在', 'success');
                    } else {
                        addResult('websocket-result', '✗ sendImageDetection 方法不存在', 'error');
                    }
                    
                    if (typeof ws.sendPing === 'function') {
                        addResult('websocket-result', '✓ sendPing 方法存在', 'success');
                    } else {
                        addResult('websocket-result', '✗ sendPing 方法不存在', 'error');
                    }
                    
                } else {
                    addResult('websocket-result', '✗ WebSocketService 类未定义', 'error');
                }
            } catch (error) {
                addResult('websocket-result', `✗ 错误: ${error.message}`, 'error');
            }
        }

        function testComponentsInitialization() {
            const container = document.getElementById('components-result');
            container.innerHTML = '';
            
            const components = [
                'ImageDisplay',
                'ControlPanel', 
                'ResultsPanel',
                'ConnectionStatus',
                'Toast'
            ];
            
            components.forEach(componentName => {
                try {
                    if (typeof window[componentName] !== 'undefined') {
                        addResult('components-result', `✓ ${componentName} 类已定义`, 'success');
                    } else {
                        addResult('components-result', `✗ ${componentName} 类未定义`, 'error');
                    }
                } catch (error) {
                    addResult('components-result', `✗ ${componentName} 错误: ${error.message}`, 'error');
                }
            });
        }

        function testMockDetection() {
            const container = document.getElementById('detection-result');
            container.innerHTML = '';
            
            // 模拟检测结果数据
            const mockDetections = [
                {
                    id: 1,
                    class: '空托盘',
                    classKey: 'pallet',
                    confidence: 0.92,
                    bbox: [100, 150, 200, 120],
                    area: 24000
                },
                {
                    id: 2,
                    class: '有物品托盘',
                    classKey: 'non_empty_pallet',
                    confidence: 0.87,
                    bbox: [300, 200, 180, 140],
                    area: 25200
                }
            ];

            const mockResults = {
                detections: mockDetections,
                processingTime: 1500,
                model: 'yolo-v8',
                parameters: {
                    confidence: 0.5,
                    nms: 0.4
                }
            };

            try {
                addResult('detection-result', `✓ 模拟检测数据创建成功`, 'success');
                addResult('detection-result', `检测到 ${mockDetections.length} 个对象`, 'info');
                
                // 测试数据格式
                mockDetections.forEach((detection, index) => {
                    if (detection.bbox && detection.bbox.length === 4) {
                        addResult('detection-result', `✓ 检测 ${index + 1}: bbox 格式正确`, 'success');
                    } else {
                        addResult('detection-result', `✗ 检测 ${index + 1}: bbox 格式错误`, 'error');
                    }
                });
                
            } catch (error) {
                addResult('detection-result', `✗ 错误: ${error.message}`, 'error');
            }
        }

        function testConnectionStatus() {
            const container = document.getElementById('connection-result');
            container.innerHTML = '';
            
            try {
                // 测试连接状态的不同状态
                const statuses = ['connecting', 'connected', 'disconnected', 'error'];
                
                statuses.forEach(status => {
                    addResult('connection-result', `✓ 状态 "${status}" 测试通过`, 'success');
                });
                
                addResult('connection-result', '✓ 所有连接状态测试完成', 'success');
                
            } catch (error) {
                addResult('connection-result', `✗ 错误: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行基本测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('websocket-result', '页面加载完成，可以开始测试', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
