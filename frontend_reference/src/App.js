/**
 * Main Application Class
 * Coordinates all components and handles global application state
 */
class App {
    constructor() {
        this.isInitialized = false;
        this.currentTab = 'controls';
        this.apiAvailable = false;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('Initializing application...');
            
            // Setup global error handling
            this.setupErrorHandling();
            
            // Setup tab navigation
            this.setupTabNavigation();
            
            // Setup header controls
            this.setupHeaderControls();
            
            // Check API availability
            await this.checkApiAvailability();
            
            // Setup keyboard shortcuts
            this.setupKeyboardShortcuts();
            
            // Setup window resize handling
            this.setupResizeHandling();
            
            // Initialize components (they're already created globally)
            this.initializeComponents();
            
            this.isInitialized = true;
            console.log('Application initialized successfully');
            
            // Show welcome message
            this.showWelcomeMessage();
            
        } catch (error) {
            console.error('Failed to initialize application:', error);
            window.toast?.error('初始化失败', '应用程序初始化时发生错误');
        }
    }

    /**
     * Setup global error handling
     */
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            window.toast?.error('应用错误', '发生了意外错误，请刷新页面重试');
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            window.toast?.error('网络错误', '网络请求失败，请检查连接');
        });
    }

    /**
     * Setup tab navigation
     */
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });
    }

    /**
     * Switch to a specific tab
     * @param {string} tabName - Tab name to switch to
     */
    switchTab(tabName) {
        // Update button states
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update content visibility
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;
    }

    /**
     * Setup header controls
     */
    setupHeaderControls() {
        const menuBtn = document.getElementById('menu-btn');
        const settingsBtn = document.getElementById('settings-btn');
        const helpBtn = document.getElementById('help-btn');

        if (menuBtn) {
            menuBtn.addEventListener('click', () => {
                this.handleMenuClick();
            });
        }

        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.handleSettingsClick();
            });
        }

        if (helpBtn) {
            helpBtn.addEventListener('click', () => {
                this.handleHelpClick();
            });
        }
    }

    /**
     * Handle menu button click
     */
    handleMenuClick() {
        window.toast?.info('菜单', '菜单功能开发中');
    }

    /**
     * Handle settings button click
     */
    handleSettingsClick() {
        window.toast?.info('设置', '设置面板开发中');
    }

    /**
     * Handle help button click
     */
    handleHelpClick() {
        const helpText = `
托盘状态检测系统使用说明：

1. 点击"加载图像"按钮选择图像文件或使用演示图像
2. 选择检测模型（YOLO v8、Faster R-CNN 等）
3. 调整置信度阈值，选择要检测的托盘类型
4. 点击"运行检测"开始检测托盘状态
5. 在"检测结果"标签页查看检测结果
6. 点击"保存结果"导出检测结果

快捷键：
- Ctrl+O: 加载图像
- Ctrl+R: 运行检测
- Ctrl+S: 保存结果
- Tab: 切换标签页
        `;
        
        window.toast?.info('帮助', helpText);
    }

    /**
     * Check API availability
     */
    async checkApiAvailability() {
        try {
            this.apiAvailable = await window.apiService.isApiAvailable();
            console.log('API available:', this.apiAvailable);
            
            if (!this.apiAvailable) {
                window.toast?.warning(
                    '离线模式', 
                    '后端 API 不可用，使用演示模式'
                );
            }
        } catch (error) {
            console.warn('API check failed:', error);
            this.apiAvailable = false;
        }
    }

    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+O: Load image
            if (event.ctrlKey && event.key === 'o') {
                event.preventDefault();
                document.getElementById('load-image-btn')?.click();
            }
            
            // Ctrl+R: Run inference
            if (event.ctrlKey && event.key === 'r') {
                event.preventDefault();
                const inferenceBtn = document.getElementById('run-inference-btn');
                if (inferenceBtn && !inferenceBtn.disabled) {
                    inferenceBtn.click();
                }
            }
            
            // Ctrl+S: Save results
            if (event.ctrlKey && event.key === 's') {
                event.preventDefault();
                const saveBtn = document.getElementById('save-results-btn');
                if (saveBtn && !saveBtn.disabled) {
                    saveBtn.click();
                }
            }
            
            // Tab: Switch tabs
            if (event.key === 'Tab' && !event.ctrlKey && !event.shiftKey) {
                const activeElement = document.activeElement;
                if (!activeElement || !activeElement.closest('.tab-content')) {
                    event.preventDefault();
                    const nextTab = this.currentTab === 'controls' ? 'results' : 'controls';
                    this.switchTab(nextTab);
                }
            }
        });
    }

    /**
     * Setup window resize handling
     */
    setupResizeHandling() {
        let resizeTimeout;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Recalculate image display canvas if image is loaded
        if (window.imageDisplay && window.imageDisplay.mainImage.style.display !== 'none') {
            window.imageDisplay.setupCanvas();
            window.imageDisplay.drawDetections();
        }
    }

    /**
     * Initialize components
     */
    initializeComponents() {
        // Components are already initialized globally
        // This method can be used for additional setup if needed
        console.log('Components initialized');
    }

    /**
     * Show welcome message
     */
    showWelcomeMessage() {
        setTimeout(() => {
            window.toast?.success(
                '欢迎使用托盘状态检测系统',
                '点击"加载图像"开始检测'
            );
        }, 1000);
    }

    /**
     * Get application state
     * @returns {Object} Application state
     */
    getState() {
        return {
            isInitialized: this.isInitialized,
            currentTab: this.currentTab,
            apiAvailable: this.apiAvailable,
            imageLoaded: window.imageDisplay?.currentImage !== null,
            wsConnected: window.wsService?.isConnected() || false
        };
    }

    /**
     * Reset application to initial state
     */
    reset() {
        // Reset image display
        window.imageDisplay?.reset();
        
        // Reset results panel
        window.resultsPanel?.clearResults();
        
        // Switch to controls tab
        this.switchTab('controls');
        
        // Clear any active toasts
        window.toast?.clear();
        
        window.toast?.info('应用重置', '应用已重置到初始状态');
    }

    /**
     * Export application data
     * @returns {Object} Application data
     */
    exportData() {
        const imageData = window.imageDisplay?.getCurrentImage();
        const results = window.resultsPanel?.getCurrentResults();
        
        return {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            state: this.getState(),
            image: imageData,
            results: results
        };
    }

    /**
     * Import application data
     * @param {Object} data - Application data to import
     */
    importData(data) {
        try {
            if (data.image && data.image.source) {
                window.imageDisplay.loadImage(data.image.source);
            }
            
            if (data.results) {
                window.resultsPanel.updateResults(data.results);
            }
            
            window.toast?.success('数据导入成功', '应用数据已恢复');
            
        } catch (error) {
            console.error('Import error:', error);
            window.toast?.error('导入失败', '数据格式不正确');
        }
    }
}

// Create global app instance
window.app = new App();
