/**
 * WebSocket Service
 * Handles real-time communication with backend
 */
class WebSocketService {
    constructor() {
        this.ws = null;
        this.url = this.getWebSocketUrl();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.isConnecting = false;
        this.eventListeners = new Map();
        
        this.connect();
    }

    /**
     * Get WebSocket URL
     * @returns {string} WebSocket URL
     */
    getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const hostname = window.location.hostname;
        const port = window.location.port;
        
        // Default to same origin with WebSocket path
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return `${protocol}//${hostname}:8000/ws`; // Assuming backend runs on port 8000
        }
        
        return `${protocol}//${hostname}${port ? ':' + port : ''}/ws`;
    }

    /**
     * Connect to WebSocket server
     */
    connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
            return;
        }

        this.isConnecting = true;
        
        try {
            this.ws = new WebSocket(this.url);
            this.setupEventHandlers();
        } catch (error) {
            console.error('WebSocket connection error:', error);
            this.isConnecting = false;
            this.scheduleReconnect();
        }
    }

    /**
     * Setup WebSocket event handlers
     */
    setupEventHandlers() {
        this.ws.onopen = (event) => {
            console.log('WebSocket connected');
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            this.emit('connected', event);
        };

        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            this.isConnecting = false;
            this.emit('disconnected', event);
            
            if (!event.wasClean) {
                this.scheduleReconnect();
            }
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.isConnecting = false;
            this.emit('error', error);
        };
    }

    /**
     * Handle incoming WebSocket messages
     * @param {Object} data - Message data
     */
    handleMessage(data) {
        const { type, payload } = data;
        
        switch (type) {
            case 'detection_progress':
                this.emit('detectionProgress', payload);
                break;
            case 'detection_complete':
                this.emit('detectionComplete', payload);
                break;
            case 'detection_error':
                this.emit('detectionError', payload);
                break;
            case 'system_status':
                this.emit('systemStatus', payload);
                break;
            case 'model_loaded':
                this.emit('modelLoaded', payload);
                break;
            default:
                this.emit('message', data);
        }
    }

    /**
     * Send message to WebSocket server
     * @param {Object} data - Message data
     */
    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        } else {
            console.warn('WebSocket not connected, message not sent:', data);
        }
    }

    /**
     * Send detection request
     * @param {Object} params - Detection parameters
     */
    sendDetectionRequest(params) {
        this.send({
            type: 'detect',
            payload: params
        });
    }

    /**
     * Send model change request
     * @param {string} modelId - Model ID
     */
    sendModelChange(modelId) {
        this.send({
            type: 'change_model',
            payload: { model: modelId }
        });
    }

    /**
     * Request system status
     */
    requestStatus() {
        this.send({
            type: 'get_status',
            payload: {}
        });
    }

    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.emit('maxReconnectAttemptsReached');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            this.connect();
        }, delay);
    }

    /**
     * Disconnect WebSocket
     */
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }
    }

    /**
     * Add event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * Remove event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * Emit event to listeners
     * @param {string} event - Event name
     * @param {*} data - Event data
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Get connection status
     * @returns {string} Connection status
     */
    getStatus() {
        if (!this.ws) return 'disconnected';
        
        switch (this.ws.readyState) {
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return 'connected';
            case WebSocket.CLOSING:
                return 'closing';
            case WebSocket.CLOSED:
                return 'disconnected';
            default:
                return 'unknown';
        }
    }

    /**
     * Check if WebSocket is connected
     * @returns {boolean} Connection status
     */
    isConnected() {
        return this.ws && this.ws.readyState === WebSocket.OPEN;
    }
}

// Create global WebSocket service instance
window.wsService = new WebSocketService();

// Setup WebSocket event listeners for UI updates
window.wsService.on('connected', () => {
    console.log('WebSocket connected to server');
    window.toast?.success('连接成功', 'WebSocket 连接已建立');
});

window.wsService.on('disconnected', () => {
    console.log('WebSocket disconnected from server');
    window.toast?.warning('连接断开', 'WebSocket 连接已断开');
});

window.wsService.on('detectionProgress', (data) => {
    console.log('Detection progress:', data);
    // Update progress UI if needed
});

window.wsService.on('detectionComplete', (data) => {
    console.log('Detection complete:', data);
    if (window.controlPanel) {
        window.controlPanel.onInferenceComplete(data);
    }
});

window.wsService.on('detectionError', (data) => {
    console.error('Detection error:', data);
    window.toast?.error('检测失败', data.message || '检测过程中发生错误');
});

window.wsService.on('systemStatus', (data) => {
    console.log('System status:', data);
    // Update system status UI
    const systemInfo = document.getElementById('system-info');
    if (systemInfo) {
        systemInfo.textContent = data.gpu_enabled ? 'GPU 已启用' : 'GPU 未启用';
    }
});
