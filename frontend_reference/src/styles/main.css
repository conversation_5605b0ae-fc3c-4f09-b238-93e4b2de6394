/**
 * =====================================
 *    托盘状态检测系统 - 设计指南
 * =====================================
 *
 * 项目: 托盘状态检测系统
 * 类型: AI科技平台
 *
 * 设计语言
 * 风格: 简洁 · 现代 · 科技感
 * 愿景: 明亮、扁平、极简界面，配以科技感强调色
 *
 * 特征:
 * - 科学性: 纯净白色配以鲜艳科技强调色
 * - 亲和力: 浅色背景配以清晰视觉层次
 * - 专业性: 现代扁平设计配以微妙深度提示
 *
 * 设计哲学:
 * 创建明亮、专注的AI分析环境，保持科学精确性的同时
 * 具有视觉吸引力和现代感。
 *
 * 参考: 现代AI平台、数据可视化仪表板
 * =====================================
 */

/* CSS Variables - Light Tech Theme */
:root {
    /* Light Tech-inspired Theme */
    --background: #F4F6FB; /* Light blue-white */
    --foreground: #262F40; /* Dark navy text */
    --card: #FFFFFF; /* White card */
    --card-foreground: #262F40; /* Dark navy text */
    --popover: #FFFFFF; /* White popover */
    --popover-foreground: #262F40; /* Dark navy text */
    --primary: #0066FF; /* Vibrant blue */
    --primary-foreground: #FFFFFF; /* White text */
    --secondary: #EFF2FD; /* Very light blue */
    --secondary-foreground: #4B5975; /* Medium navy */
    --muted: #EEF0F5; /* Light grayish blue */
    --muted-foreground: #7D8494; /* Medium gray */
    --accent: #6200EA; /* Deep purple */
    --accent-foreground: #FFFFFF; /* White */
    --destructive: #EF4444; /* Red */
    --destructive-foreground: #FFFFFF; /* White */
    --border: #DEE3F0; /* Soft blue-gray border */
    --input: #DEE3F0; /* Soft blue-gray input */
    --ring: #0066FF; /* Vibrant blue */

    /* Tech-inspired chart colors */
    --chart-1: #0066FF; /* Vibrant blue */
    --chart-2: #6200EA; /* Deep purple */
    --chart-3: #00B3D4; /* Cyan */
    --chart-4: #9C27FF; /* Bright purple */
    --chart-5: #00CC9A; /* Teal */

    /* Sidebar specific colors */
    --sidebar-background: #263254; /* Dark navy sidebar */
    --sidebar-foreground: #FFFFFF; /* White text */
    --sidebar-primary: #0066FF; /* Vibrant blue */
    --sidebar-primary-foreground: #FFFFFF; /* White text */
    --sidebar-accent: #6200EA; /* Deep purple */
    --sidebar-accent-foreground: #FFFFFF; /* White */
    --sidebar-border: #3E4A67; /* Medium navy border */
    --sidebar-ring: #0066FF; /* Vibrant blue */

    /* Pallet detection specific colors */
    --pallet-empty: #28a745; /* Green for empty pallets */
    --pallet-loaded: #ffc107; /* Yellow for loaded pallets */

    /* Shadow and depth */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    --radius: 0.5rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    height: 100vh;
    overflow: hidden;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--background);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* Button Styles */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 36px;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
    box-shadow: var(--shadow);
}

.btn-primary:hover:not(:disabled) {
    background-color: color-mix(in srgb, var(--primary) 90%, black);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background-color: var(--chart-5);
    color: var(--primary-foreground);
    box-shadow: var(--shadow);
}

.btn-success:hover:not(:disabled) {
    background-color: color-mix(in srgb, var(--chart-5) 90%, black);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
    background-color: color-mix(in srgb, var(--secondary) 80%, black);
    box-shadow: var(--shadow);
}

.icon-btn {
    background: none;
    border: none;
    color: var(--sidebar-foreground);
    cursor: pointer;
    padding: 10px;
    border-radius: var(--radius);
    transition: all 0.2s ease;
    min-width: 44px;
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.icon-btn:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
}

.icon {
    font-size: 18px;
}

/* Form Controls */
.slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: none;
}

.model-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background-color: var(--background);
    color: var(--foreground);
    font-size: 14px;
    box-shadow: var(--shadow-sm);
}

.model-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px color-mix(in srgb, var(--primary) 20%, transparent);
}

/* Loading Animation */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */

/* Tablet Landscape (1024px and below) */
@media (max-width: 1024px) {
    .control-section {
        width: 45%;
    }

    .app-title {
        font-size: 16px;
    }

    .header {
        height: 64px;
        padding: 0 20px;
    }

    .icon-btn {
        min-width: 48px;
        min-height: 48px;
        padding: 12px;
    }
}

/* Tablet Portrait (768px and below) */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .image-section {
        height: 45vh;
        padding: 12px;
    }

    .control-section {
        width: 100%;
        height: 55vh;
        padding: 16px;
        border-left: none;
        border-top: 1px solid #444;
    }

    .header {
        height: 56px;
        padding: 0 16px;
    }

    .icon-btn {
        min-width: 44px;
        min-height: 44px;
        padding: 10px;
    }

    .app-title {
        font-size: 15px;
    }
}

/* Mobile (480px and below) */
@media (max-width: 480px) {
    .image-section {
        height: 40vh;
        padding: 8px;
    }

    .control-section {
        height: 60vh;
        padding: 12px;
    }

    .header {
        height: 52px;
        padding: 0 12px;
    }

    .header-left {
        gap: 8px;
    }

    .header-right {
        gap: 6px;
    }

    .icon-btn {
        min-width: 40px;
        min-height: 40px;
        padding: 8px;
    }

    .app-title {
        font-size: 14px;
    }
}

/* Touch and Accessibility Optimizations */
* {
    -webkit-tap-highlight-color: transparent;
}

button, input, select, textarea {
    touch-action: manipulation;
}

/* Larger scrollbars for touch devices */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #404040;
}

::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 6px;
    border: 2px solid #404040;
}

::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Touch-friendly focus indicators */
*:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Prevent text selection on UI elements */
.tab-btn, .action-btn, .icon-btn, .class-label, .results-action-btn {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
