/**
 * Image Display Component
 * Handles image loading, display, and detection visualization
 */
class ImageDisplay {
    constructor() {
        this.imageContainer = document.getElementById('image-container');
        this.placeholder = document.getElementById('placeholder');
        this.mainImage = document.getElementById('main-image');
        this.detectionCanvas = document.getElementById('detection-canvas');
        this.loadingOverlay = document.getElementById('loading-overlay');
        
        this.currentImage = null;
        this.detections = [];
        this.canvasContext = null;
        
        this.init();
    }

    init() {
        // Initialize canvas context
        this.canvasContext = this.detectionCanvas.getContext('2d');
        
        // Handle image load events
        this.mainImage.addEventListener('load', () => {
            this.onImageLoaded();
        });

        // Handle image error events
        this.mainImage.addEventListener('error', () => {
            this.showError('图像加载失败');
        });
    }

    /**
     * Load an image from URL or File
     * @param {string|File} source - Image source (URL string or File object)
     */
    loadImage(source) {
        this.showLoading(false);
        this.clearDetections();
        
        if (source instanceof File) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.mainImage.src = e.target.result;
                this.currentImage = source;
            };
            reader.onerror = () => {
                this.showError('文件读取失败');
            };
            reader.readAsDataURL(source);
        } else if (typeof source === 'string') {
            this.mainImage.src = source;
            this.currentImage = source;
        }
    }

    /**
     * Called when image is successfully loaded
     */
    onImageLoaded() {
        this.placeholder.style.display = 'none';
        this.mainImage.style.display = 'block';
        this.setupCanvas();
        
        // Enable inference button
        const inferenceBtn = document.getElementById('run-inference-btn');
        if (inferenceBtn) {
            inferenceBtn.disabled = false;
        }
        
        // Dispatch image loaded event
        this.dispatchEvent('imageLoaded', {
            image: this.currentImage,
            width: this.mainImage.naturalWidth,
            height: this.mainImage.naturalHeight
        });
    }

    /**
     * Setup canvas to match image dimensions
     */
    setupCanvas() {
        const rect = this.mainImage.getBoundingClientRect();
        const containerRect = this.imageContainer.getBoundingClientRect();
        
        // Calculate image position within container
        const imageAspectRatio = this.mainImage.naturalWidth / this.mainImage.naturalHeight;
        const containerAspectRatio = containerRect.width / containerRect.height;
        
        let displayWidth, displayHeight, offsetX, offsetY;
        
        if (imageAspectRatio > containerAspectRatio) {
            // Image is wider than container
            displayWidth = containerRect.width;
            displayHeight = containerRect.width / imageAspectRatio;
            offsetX = 0;
            offsetY = (containerRect.height - displayHeight) / 2;
        } else {
            // Image is taller than container
            displayWidth = containerRect.height * imageAspectRatio;
            displayHeight = containerRect.height;
            offsetX = (containerRect.width - displayWidth) / 2;
            offsetY = 0;
        }
        
        this.detectionCanvas.width = displayWidth;
        this.detectionCanvas.height = displayHeight;
        this.detectionCanvas.style.width = displayWidth + 'px';
        this.detectionCanvas.style.height = displayHeight + 'px';
        this.detectionCanvas.style.left = offsetX + 'px';
        this.detectionCanvas.style.top = offsetY + 'px';
        this.detectionCanvas.style.display = 'block';
        
        // Store scaling factors for detection drawing
        this.scaleX = displayWidth / this.mainImage.naturalWidth;
        this.scaleY = displayHeight / this.mainImage.naturalHeight;
    }

    /**
     * Show loading state
     * @param {boolean} processing - Whether to show processing state
     */
    showLoading(processing = true) {
        this.loadingOverlay.style.display = 'flex';
        const loadingText = this.loadingOverlay.querySelector('span');
        if (loadingText) {
            loadingText.textContent = processing ? '处理中...' : '加载中...';
        }
    }

    /**
     * Hide loading state
     */
    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }

    /**
     * Show error state
     * @param {string} message - Error message
     */
    showError(message) {
        this.hideLoading();
        this.placeholder.style.display = 'flex';
        this.placeholder.querySelector('.placeholder-text').textContent = message;
        this.mainImage.style.display = 'none';
        this.detectionCanvas.style.display = 'none';
    }

    /**
     * Display detection results
     * @param {Array} detections - Array of detection objects
     */
    showDetections(detections) {
        this.detections = detections;
        this.drawDetections();
    }

    /**
     * Draw detection bounding boxes and labels
     */
    drawDetections() {
        if (!this.canvasContext || !this.detections.length) return;
        
        // Clear canvas
        this.canvasContext.clearRect(0, 0, this.detectionCanvas.width, this.detectionCanvas.height);
        
        // Set drawing styles
        this.canvasContext.strokeStyle = '#007bff';
        this.canvasContext.lineWidth = 2;
        this.canvasContext.fillStyle = '#007bff';
        this.canvasContext.font = '14px Arial';
        
        this.detections.forEach((detection, index) => {
            const { bbox, class: className, confidence } = detection;
            const [x, y, width, height] = bbox;
            
            // Scale coordinates to canvas size
            const scaledX = x * this.scaleX;
            const scaledY = y * this.scaleY;
            const scaledWidth = width * this.scaleX;
            const scaledHeight = height * this.scaleY;
            
            // Draw bounding box
            this.canvasContext.strokeRect(scaledX, scaledY, scaledWidth, scaledHeight);
            
            // Draw label background
            const label = `${className} ${(confidence * 100).toFixed(1)}%`;
            const labelMetrics = this.canvasContext.measureText(label);
            const labelHeight = 20;
            
            this.canvasContext.fillStyle = '#007bff';
            this.canvasContext.fillRect(scaledX, scaledY - labelHeight, labelMetrics.width + 8, labelHeight);
            
            // Draw label text
            this.canvasContext.fillStyle = 'white';
            this.canvasContext.fillText(label, scaledX + 4, scaledY - 6);
        });
    }

    /**
     * Clear detection overlays
     */
    clearDetections() {
        this.detections = [];
        if (this.canvasContext) {
            this.canvasContext.clearRect(0, 0, this.detectionCanvas.width, this.detectionCanvas.height);
        }
    }

    /**
     * Reset to initial state
     */
    reset() {
        this.currentImage = null;
        this.clearDetections();
        this.hideLoading();
        this.placeholder.style.display = 'flex';
        this.placeholder.querySelector('.placeholder-text').textContent = '点击加载图像开始检测';
        this.mainImage.style.display = 'none';
        this.detectionCanvas.style.display = 'none';
        
        // Disable inference button
        const inferenceBtn = document.getElementById('run-inference-btn');
        if (inferenceBtn) {
            inferenceBtn.disabled = true;
        }
    }

    /**
     * Dispatch custom event
     * @param {string} eventName - Event name
     * @param {Object} detail - Event detail data
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    /**
     * Get current image data
     * @returns {Object} Current image information
     */
    getCurrentImage() {
        return {
            source: this.currentImage,
            width: this.mainImage.naturalWidth,
            height: this.mainImage.naturalHeight,
            detections: this.detections
        };
    }
}

// Create global image display instance
window.imageDisplay = new ImageDisplay();
