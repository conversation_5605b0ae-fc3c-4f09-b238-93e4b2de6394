/**
 * Image Display Component
 * Handles image loading, display, and detection visualization
 */
class ImageDisplay {
    constructor() {
        this.imageContainer = document.getElementById('image-container');
        this.placeholder = document.getElementById('placeholder');
        this.mainImage = document.getElementById('main-image');
        this.detectionCanvas = document.getElementById('detection-canvas');
        this.loadingOverlay = document.getElementById('loading-overlay');
        
        this.currentImage = null;
        this.detections = [];
        this.canvasContext = null;
        this.highlightedDetection = -1;
        this.scaleX = 1;
        this.scaleY = 1;
        
        this.init();
    }

    init() {
        // Initialize canvas context
        this.canvasContext = this.detectionCanvas.getContext('2d');
        
        // Handle image load events
        this.mainImage.addEventListener('load', () => {
            this.onImageLoaded();
        });

        // Handle image error events
        this.mainImage.addEventListener('error', () => {
            this.showError('图像加载失败');
        });

        // Handle canvas click for detection interaction
        this.detectionCanvas.addEventListener('click', (e) => {
            this.handleCanvasClick(e);
        });
    }

    /**
     * Load an image from URL or File
     * @param {string|File} source - Image source (URL string or File object)
     */
    loadImage(source) {
        this.showLoading(false);
        this.clearDetections();
        
        if (source instanceof File) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.mainImage.src = e.target.result;
                this.currentImage = source;
            };
            reader.onerror = () => {
                this.showError('文件读取失败');
            };
            reader.readAsDataURL(source);
        } else if (typeof source === 'string') {
            this.mainImage.src = source;
            this.currentImage = source;
        }
    }

    /**
     * Called when image is successfully loaded
     */
    onImageLoaded() {
        this.placeholder.style.display = 'none';
        this.mainImage.style.display = 'block';
        this.setupCanvas();
        
        // Enable inference button
        const inferenceBtn = document.getElementById('run-inference-btn');
        if (inferenceBtn) {
            inferenceBtn.disabled = false;
        }
        
        // Dispatch image loaded event
        this.dispatchEvent('imageLoaded', {
            image: this.currentImage,
            width: this.mainImage.naturalWidth,
            height: this.mainImage.naturalHeight
        });
    }

    /**
     * Setup canvas to match image dimensions
     */
    setupCanvas() {
        const containerRect = this.imageContainer.getBoundingClientRect();
        
        // Calculate image position within container
        const imageAspectRatio = this.mainImage.naturalWidth / this.mainImage.naturalHeight;
        const containerAspectRatio = containerRect.width / containerRect.height;
        
        let displayWidth, displayHeight, offsetX, offsetY;
        
        if (imageAspectRatio > containerAspectRatio) {
            // Image is wider than container
            displayWidth = containerRect.width;
            displayHeight = containerRect.width / imageAspectRatio;
            offsetX = 0;
            offsetY = (containerRect.height - displayHeight) / 2;
        } else {
            // Image is taller than container
            displayWidth = containerRect.height * imageAspectRatio;
            displayHeight = containerRect.height;
            offsetX = (containerRect.width - displayWidth) / 2;
            offsetY = 0;
        }
        
        this.detectionCanvas.width = displayWidth;
        this.detectionCanvas.height = displayHeight;
        this.detectionCanvas.style.width = displayWidth + 'px';
        this.detectionCanvas.style.height = displayHeight + 'px';
        this.detectionCanvas.style.left = offsetX + 'px';
        this.detectionCanvas.style.top = offsetY + 'px';
        this.detectionCanvas.style.display = 'block';
        
        // Store scaling factors for detection drawing
        this.scaleX = displayWidth / this.mainImage.naturalWidth;
        this.scaleY = displayHeight / this.mainImage.naturalHeight;
    }

    /**
     * Show loading state
     * @param {boolean} processing - Whether to show processing state
     */
    showLoading(processing = true) {
        this.loadingOverlay.style.display = 'flex';
        const loadingText = this.loadingOverlay.querySelector('span');
        if (loadingText) {
            loadingText.textContent = processing ? '处理中...' : '加载中...';
        }
    }

    /**
     * Hide loading state
     */
    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }

    /**
     * Show error state
     * @param {string} message - Error message
     */
    showError(message) {
        this.hideLoading();
        this.placeholder.style.display = 'flex';
        this.placeholder.querySelector('.placeholder-text').textContent = message;
        this.mainImage.style.display = 'none';
        this.detectionCanvas.style.display = 'none';
    }

    /**
     * Display detection results
     * @param {Array} detections - Array of detection objects
     */
    showDetections(detections) {
        this.detections = detections;
        this.drawDetections();
    }

    /**
     * Update detections with image size information
     * @param {Array} detections - Array of detection objects
     * @param {Array} imageSize - [width, height] of original image
     */
    updateDetections(detections, imageSize) {
        this.detections = detections;
        this.originalImageSize = imageSize;
        this.drawDetections();
    }

    /**
     * Draw detection bounding boxes and labels
     */
    drawDetections() {
        if (!this.canvasContext || !this.detections.length) return;

        // Clear canvas
        this.canvasContext.clearRect(0, 0, this.detectionCanvas.width, this.detectionCanvas.height);

        this.detections.forEach((detection, index) => {
            const { bbox, class: className, confidence, id } = detection;
            const [x, y, width, height] = bbox;

            // Get color for this detection class
            const color = this.getColorForClass(id || index);
            const isHighlighted = index === this.highlightedDetection;

            // Scale coordinates to canvas size
            const scaledX = x * this.scaleX;
            const scaledY = y * this.scaleY;
            const scaledWidth = width * this.scaleX;
            const scaledHeight = height * this.scaleY;

            // Draw semi-transparent fill
            this.canvasContext.fillStyle = color + (isHighlighted ? '40' : '20'); // More opacity for highlighted
            this.canvasContext.fillRect(scaledX, scaledY, scaledWidth, scaledHeight);

            // Draw bounding box
            this.canvasContext.strokeStyle = color;
            this.canvasContext.lineWidth = isHighlighted ? 5 : 3; // Thicker line for highlighted
            this.canvasContext.strokeRect(scaledX, scaledY, scaledWidth, scaledHeight);

            // Draw highlight border if highlighted
            if (isHighlighted) {
                this.canvasContext.strokeStyle = '#FFD700'; // Gold color
                this.canvasContext.lineWidth = 2;
                this.canvasContext.setLineDash([5, 5]);
                this.canvasContext.strokeRect(scaledX - 3, scaledY - 3, scaledWidth + 6, scaledHeight + 6);
                this.canvasContext.setLineDash([]); // Reset dash
            }

            // Draw label
            const label = `${className} (${(confidence * 100).toFixed(1)}%)`;
            this.canvasContext.font = '14px Arial';
            const labelMetrics = this.canvasContext.measureText(label);
            const labelHeight = 20;
            const labelY = scaledY > 30 ? scaledY - 10 : scaledY + 20;

            // Draw label background
            this.canvasContext.fillStyle = color;
            this.canvasContext.fillRect(scaledX, labelY - 15, labelMetrics.width + 10, labelHeight);

            // Draw label text
            this.canvasContext.fillStyle = 'white';
            this.canvasContext.fillText(label, scaledX + 5, labelY);
        });
    }

    /**
     * Get color for detection class
     * @param {number} classId - Class ID or index
     * @returns {string} Color hex code
     */
    getColorForClass(classId) {
        const colors = [
            '#FF6B6B',  // 珊瑚红
            '#4ECDC4',  // 青绿色
            '#45B7D1',  // 天蓝色
            '#FF9F43',  // 橙色
            '#6C5CE7',  // 紫色
            '#A29BFE',  // 淡紫色
            '#FD79A8',  // 粉色
            '#00B894',  // 深绿色
            '#FDCB6E',  // 金黄色
            '#E17055',  // 棕橙色
            '#74B9FF',  // 亮蓝色
            '#81ECEC',  // 浅蓝绿
            '#55A3FF',  // 蓝色
            '#FF7675',  // 淡红色
            '#00CECA'   // 青色
        ];
        return colors[classId % colors.length];
    }

    /**
     * Handle canvas click for detection interaction
     * @param {MouseEvent} e - Click event
     */
    handleCanvasClick(e) {
        if (!this.detections.length) return;

        const rect = this.detectionCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Find clicked detection
        for (let i = 0; i < this.detections.length; i++) {
            const detection = this.detections[i];
            const [dx, dy, width, height] = detection.bbox;

            const scaledX = dx * this.scaleX;
            const scaledY = dy * this.scaleY;
            const scaledWidth = width * this.scaleX;
            const scaledHeight = height * this.scaleY;

            if (x >= scaledX && x <= scaledX + scaledWidth &&
                y >= scaledY && y <= scaledY + scaledHeight) {
                this.highlightDetection(i);
                break;
            }
        }
    }

    /**
     * Highlight a specific detection
     * @param {number} index - Detection index
     */
    highlightDetection(index) {
        this.highlightedDetection = index;
        this.drawDetections();

        // Dispatch event for other components to listen
        const event = new CustomEvent('detectionHighlighted', {
            detail: {
                index,
                detection: this.detections[index]
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Clear detection overlays
     */
    clearDetections() {
        this.detections = [];
        if (this.canvasContext) {
            this.canvasContext.clearRect(0, 0, this.detectionCanvas.width, this.detectionCanvas.height);
        }
    }

    /**
     * Reset to initial state
     */
    reset() {
        this.currentImage = null;
        this.clearDetections();
        this.hideLoading();
        this.placeholder.style.display = 'flex';
        this.placeholder.querySelector('.placeholder-text').textContent = '点击加载图像开始检测';
        this.mainImage.style.display = 'none';
        this.detectionCanvas.style.display = 'none';
        
        // Disable inference button
        const inferenceBtn = document.getElementById('run-inference-btn');
        if (inferenceBtn) {
            inferenceBtn.disabled = true;
        }
    }

    /**
     * Dispatch custom event
     * @param {string} eventName - Event name
     * @param {Object} detail - Event detail data
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    /**
     * Get current image data
     * @returns {Object} Current image information
     */
    getCurrentImage() {
        return {
            source: this.currentImage,
            width: this.mainImage.naturalWidth,
            height: this.mainImage.naturalHeight,
            detections: this.detections
        };
    }
}

// Create global image display instance
window.imageDisplay = new ImageDisplay();
