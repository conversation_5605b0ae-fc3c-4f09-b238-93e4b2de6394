/**
 * Control Panel Component
 * Handles user interactions and controls
 */
class ControlPanel {
    constructor() {
        this.loadImageBtn = document.getElementById('load-image-btn');
        this.fileInput = document.getElementById('file-input');
        this.runInferenceBtn = document.getElementById('run-inference-btn');
        this.saveResultsBtn = document.getElementById('save-results-btn');
        this.settingsBtnPanel = document.getElementById('settings-btn-panel');
        this.modelSelect = document.getElementById('model-select');
        this.confidenceSlider = document.getElementById('confidence-slider');
        this.confidenceValue = document.getElementById('confidence-value');
        this.realtimeToggle = document.getElementById('realtime-toggle');

        this.isProcessing = false;
        this.currentModel = 'yolo-v8';
        this.confidenceThreshold = 0.65;
        this.realtimeProcessing = false;
        this.enabledClasses = new Set(['pallet', 'non_empty_pallet']);

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateSliderValues();
        this.initializeControls();
    }

    /**
     * Initialize control states
     */
    initializeControls() {
        // Set initial slider value
        this.confidenceSlider.value = this.confidenceThreshold;

        // Set initial toggle state
        this.realtimeToggle.checked = this.realtimeProcessing;

        // Set initial class checkboxes
        const classIds = ['pallet', 'non-empty-pallet'];
        const classKeyMap = {
            'pallet': 'pallet',
            'non-empty-pallet': 'non_empty_pallet'
        };

        classIds.forEach(classId => {
            const checkbox = document.getElementById(`class-${classId}`);
            if (checkbox) {
                const classKey = classKeyMap[classId];
                checkbox.checked = this.enabledClasses.has(classKey);
            }
        });
    }

    setupEventListeners() {
        // Load image button
        this.loadImageBtn.addEventListener('click', () => {
            this.handleLoadImage();
        });

        // File input change
        this.fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e);
        });

        // Run inference button
        this.runInferenceBtn.addEventListener('click', () => {
            this.handleRunInference();
        });

        // Save results button
        this.saveResultsBtn.addEventListener('click', () => {
            this.handleSaveResults();
        });

        // Settings button in panel
        if (this.settingsBtnPanel) {
            this.settingsBtnPanel.addEventListener('click', () => {
                this.handleSettingsClick();
            });
        }

        // Model selection
        if (this.modelSelect) {
            this.modelSelect.addEventListener('change', (e) => {
                this.handleModelChange(e);
            });
        }

        // Confidence slider
        this.confidenceSlider.addEventListener('input', (e) => {
            this.handleConfidenceChange(e);
        });

        // Real-time processing toggle
        this.realtimeToggle.addEventListener('change', (e) => {
            this.handleRealtimeToggle(e);
        });

        // Detection class checkboxes
        this.setupClassCheckboxes();

        // Listen for image loaded events
        document.addEventListener('imageLoaded', () => {
            this.onImageLoaded();
        });

        // Listen for inference complete events
        document.addEventListener('inferenceComplete', (e) => {
            this.onInferenceComplete(e.detail);
        });
    }

    /**
     * Handle load image button click
     */
    handleLoadImage() {
        // Show sample image options or trigger file input
        const useDemo = confirm('是否使用演示图像？点击"取消"选择本地文件。');
        
        if (useDemo) {
            this.loadDemoImage();
        } else {
            this.fileInput.click();
        }
    }

    /**
     * Load demo image
     */
    loadDemoImage() {
        const demoImageUrl = 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=1200&h=800&fit=crop';
        window.imageDisplay.loadImage(demoImageUrl);
        
        window.toast.success('图像已加载', '演示图像加载成功，可以开始检测');
    }

    /**
     * Handle file selection
     * @param {Event} event - File input change event
     */
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            window.toast.error('文件类型错误', '请选择图像文件');
            return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            window.toast.error('文件过大', '图像文件大小不能超过 10MB');
            return;
        }

        window.imageDisplay.loadImage(file);
        window.toast.success('图像已加载', `已加载文件: ${file.name}`);
    }

    /**
     * Handle run inference button click
     */
    async handleRunInference() {
        if (this.isProcessing) return;

        const imageData = window.imageDisplay.getCurrentImage();
        if (!imageData.source) {
            window.toast.error('未选择图像', '请先加载图像');
            return;
        }

        // Check WebSocket connection
        if (!window.wsService.isConnected()) {
            window.toast.error('连接断开', '请等待 WebSocket 连接建立');
            return;
        }

        this.setProcessingState(true);
        window.imageDisplay.showLoading(true);

        try {
            // Send image detection request via WebSocket
            window.wsService.sendImageDetection(imageData.source);
            window.toast.info('检测请求已发送', '正在处理图像...');

        } catch (error) {
            console.error('Inference error:', error);
            window.toast.error('检测失败', error.message || '发送检测请求时发生错误');
            this.setProcessingState(false);
            window.imageDisplay.hideLoading();
        }
    }

    /**
     * Run inference (mock implementation)
     * @param {Object} params - Inference parameters
     * @returns {Promise<Object>} Inference results
     */
    async runInference(params) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Mock detection results
        const allDetections = [
            { id: 1, class: '空托盘', classKey: 'pallet', confidence: 0.92, bbox: [100, 150, 200, 120], area: 24000 },
            { id: 2, class: '有物品托盘', classKey: 'non_empty_pallet', confidence: 0.87, bbox: [300, 200, 180, 140], area: 25200 },
            { id: 3, class: '空托盘', classKey: 'pallet', confidence: 0.85, bbox: [450, 80, 190, 110], area: 20900 },
            { id: 4, class: '有物品托盘', classKey: 'non_empty_pallet', confidence: 0.91, bbox: [150, 300, 200, 130], area: 26000 },
            { id: 5, class: '空托盘', classKey: 'pallet', confidence: 0.78, bbox: [50, 250, 180, 115], area: 20700 },
            { id: 6, class: '有物品托盘', classKey: 'non_empty_pallet', confidence: 0.89, bbox: [380, 180, 195, 125], area: 24375 }
        ];

        // Filter by confidence and enabled classes
        const filteredDetections = allDetections.filter(d =>
            d.confidence >= this.confidenceThreshold &&
            this.enabledClasses.has(d.classKey)
        );

        const mockResults = {
            detections: filteredDetections,
            processingTime: Math.random() * 1000 + 500,
            model: params.model,
            parameters: {
                confidence: params.confidence,
                enabledClasses: params.enabledClasses
            }
        };

        return mockResults;
    }

    /**
     * Handle save results button click
     */
    handleSaveResults() {
        const imageData = window.imageDisplay.getCurrentImage();
        if (!imageData.detections || imageData.detections.length === 0) {
            window.toast.warning('无结果可保存', '请先运行检测');
            return;
        }

        // Create results data
        const results = {
            timestamp: new Date().toISOString(),
            model: this.currentModel,
            parameters: {
                confidence: this.confidenceThreshold,
                nms: this.nmsThreshold
            },
            image: {
                width: imageData.width,
                height: imageData.height
            },
            detections: imageData.detections
        };

        // Download as JSON
        this.downloadResults(results);
        window.toast.success('结果已保存', '检测结果已下载为 JSON 文件');
    }

    /**
     * Download results as JSON file
     * @param {Object} results - Results data
     */
    downloadResults(results) {
        const dataStr = JSON.stringify(results, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `detection_results_${new Date().getTime()}.json`;
        link.click();
        
        URL.revokeObjectURL(link.href);
    }

    /**
     * Handle model selection change
     * @param {Event} event - Select change event
     */
    handleModelChange(event) {
        this.currentModel = event.target.value;
        window.toast.info('模型已选择', `当前模型: ${this.getModelDisplayName(this.currentModel)}`);
    }

    /**
     * Get display name for model
     * @param {string} modelId - Model ID
     * @returns {string} Display name
     */
    getModelDisplayName(modelId) {
        const modelNames = {
            'yolo-v8': 'YOLO v8',
            'faster-rcnn': 'Faster R-CNN',
            'ssd': 'SSD MobileNet',
            'efficientdet': 'EfficientDet'
        };
        return modelNames[modelId] || modelId;
    }

    /**
     * Handle confidence threshold change
     * @param {Event} event - Slider input event
     */
    handleConfidenceChange(event) {
        this.confidenceThreshold = parseFloat(event.target.value);
        this.confidenceValue.textContent = Math.round(this.confidenceThreshold * 100) + '%';

        // Update slider track color
        this.updateSliderTrack();

        // If real-time processing is enabled, trigger detection
        if (this.realtimeProcessing && window.imageDisplay.currentImage) {
            this.debounceInference();
        }
    }

    /**
     * Handle real-time processing toggle
     * @param {Event} event - Checkbox change event
     */
    handleRealtimeToggle(event) {
        this.realtimeProcessing = event.target.checked;
        window.toast?.info(
            '实时处理',
            this.realtimeProcessing ? '已启用实时处理' : '已禁用实时处理'
        );
    }

    /**
     * Setup detection class checkboxes
     */
    setupClassCheckboxes() {
        const classIds = ['pallet', 'non-empty-pallet'];
        const classKeyMap = {
            'pallet': 'pallet',
            'non-empty-pallet': 'non_empty_pallet'
        };

        classIds.forEach(classId => {
            const checkbox = document.getElementById(`class-${classId}`);
            if (checkbox) {
                checkbox.addEventListener('change', (e) => {
                    const classKey = classKeyMap[classId];
                    this.handleClassToggle(classKey, e.target.checked);
                });
            }
        });
    }

    /**
     * Handle detection class toggle
     * @param {string} className - Class name
     * @param {boolean} enabled - Whether class is enabled
     */
    handleClassToggle(className, enabled) {
        if (enabled) {
            this.enabledClasses.add(className);
        } else {
            this.enabledClasses.delete(className);
        }

        // If real-time processing is enabled, trigger detection
        if (this.realtimeProcessing && window.imageDisplay.currentImage) {
            this.debounceInference();
        }
    }

    /**
     * Handle settings button click
     */
    handleSettingsClick() {
        window.toast?.info('设置', '设置面板开发中');
    }

    /**
     * Update slider track color based on value
     */
    updateSliderTrack() {
        const percentage = this.confidenceThreshold * 100;
        const slider = this.confidenceSlider;

        // Update the ::before pseudo-element width to show filled range
        slider.style.setProperty('--slider-progress', `${percentage}%`);
    }

    /**
     * Debounced inference for real-time processing
     */
    debounceInference() {
        if (this.inferenceTimeout) {
            clearTimeout(this.inferenceTimeout);
        }

        this.inferenceTimeout = setTimeout(() => {
            this.handleRunInference();
        }, 500);
    }

    /**
     * Update slider value displays
     */
    updateSliderValues() {
        this.confidenceValue.textContent = Math.round(this.confidenceThreshold * 100) + '%';
        this.updateSliderTrack();
    }

    /**
     * Called when image is loaded
     */
    onImageLoaded() {
        this.runInferenceBtn.disabled = false;
        this.saveResultsBtn.disabled = true;
    }

    /**
     * Called when inference is complete
     * @param {Object} results - Inference results
     */
    onInferenceComplete(results) {
        this.setProcessingState(false);
        window.imageDisplay.hideLoading();
        window.imageDisplay.showDetections(results.detections);
        
        // Update results panel
        window.resultsPanel.updateResults(results);
        
        // Enable save button
        this.saveResultsBtn.disabled = false;
        
        // Show success message
        window.toast.success(
            '检测完成',
            `检测到 ${results.detections.length} 个对象，用时 ${Math.round(results.processingTime)}ms`
        );

        // Dispatch event
        const event = new CustomEvent('inferenceComplete', { detail: results });
        document.dispatchEvent(event);
    }

    /**
     * Set processing state
     * @param {boolean} processing - Whether processing is active
     */
    setProcessingState(processing) {
        this.isProcessing = processing;
        this.runInferenceBtn.disabled = processing;
        this.loadImageBtn.disabled = processing;
        
        // Update status
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = processing ? '处理中' : '就绪';
            statusText.className = processing ? 'status-text processing' : 'status-text ready';
        }
    }
}

// Create global control panel instance
window.controlPanel = new ControlPanel();
