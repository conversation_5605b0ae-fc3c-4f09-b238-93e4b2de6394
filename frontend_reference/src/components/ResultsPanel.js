/**
 * Results Panel Component
 * Displays detection results and statistics
 */
class ResultsPanel {
    constructor() {
        this.objectsCount = document.getElementById('objects-count');
        this.processingTimeDisplay = document.getElementById('processing-time-display');
        this.classesCount = document.getElementById('classes-count');
        this.resultsTableBody = document.getElementById('results-table-body');
        this.downloadBtn = document.getElementById('download-results-btn');
        this.shareBtn = document.getElementById('share-results-btn');

        this.currentResults = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.showNoResults();
    }

    setupEventListeners() {
        // Download button
        this.downloadBtn.addEventListener('click', () => {
            this.handleDownload();
        });

        // Share button
        this.shareBtn.addEventListener('click', () => {
            this.handleShare();
        });

        // Listen for table row clicks
        this.resultsTableBody.addEventListener('click', (e) => {
            const row = e.target.closest('tr');
            if (row && !row.classList.contains('no-results-row')) {
                this.handleRowClick(row);
            }
        });

        // Listen for detection highlights from image display
        document.addEventListener('detectionHighlighted', (e) => {
            this.handleDetectionHighlighted(e.detail.index);
        });
    }

    /**
     * Update results display
     * @param {Object} results - Detection results
     */
    updateResults(results) {
        this.currentResults = results;

        // Update statistics
        const count = results.detections.length;
        const uniqueClasses = new Set(results.detections.map(d => d.class)).size;

        this.objectsCount.textContent = count.toString();
        this.processingTimeDisplay.textContent = `${Math.round(results.processingTime)} ms`;
        this.classesCount.textContent = uniqueClasses.toString();

        // Update table
        if (count > 0) {
            this.renderTable(results.detections);
        } else {
            this.showNoResults();
        }
    }

    /**
     * Render detection results in table format
     * @param {Array} detections - Array of detection objects
     */
    renderTable(detections) {
        // Sort detections by confidence (highest first)
        const sortedDetections = [...detections].sort((a, b) => b.confidence - a.confidence);

        // Clear existing rows
        this.resultsTableBody.innerHTML = '';

        // Create table rows
        sortedDetections.forEach((detection, index) => {
            const row = this.createTableRow(detection, index);
            this.resultsTableBody.appendChild(row);
        });
    }

    /**
     * Create a table row element
     * @param {Object} detection - Detection object
     * @param {number} index - Item index
     * @returns {HTMLElement} Table row element
     */
    createTableRow(detection, index) {
        const row = document.createElement('tr');
        row.setAttribute('data-detection-id', detection.id);
        row.setAttribute('data-index', index);

        const { class: className, confidence, bbox, classKey } = detection;
        const [x, y, width, height] = bbox;

        // Add class-specific styling class
        if (classKey === 'pallet') {
            row.classList.add('pallet-row');
        } else if (classKey === 'non_empty_pallet') {
            row.classList.add('non-empty-pallet-row');
        }

        row.innerHTML = `
            <td class="col-class">
                <span class="class-name">${className}</span>
            </td>
            <td class="col-confidence">
                <span class="confidence-value">${Math.round(confidence * 100)}%</span>
            </td>
            <td class="col-position">
                <span class="position-value">x:${Math.round(x)}, y:${Math.round(y)}, w:${Math.round(width)}, h:${Math.round(height)}</span>
            </td>
        `;

        return row;
    }

    /**
     * Handle table row click
     * @param {HTMLElement} row - Clicked table row
     */
    handleRowClick(row) {
        const detectionId = parseInt(row.getAttribute('data-detection-id'));
        const index = parseInt(row.getAttribute('data-index'));

        // Remove previous highlights
        this.clearHighlights();

        // Highlight clicked row
        row.classList.add('highlighted');

        // Highlight corresponding detection on image
        this.highlightDetection(detectionId, index);
    }

    /**
     * Handle download button click
     */
    handleDownload() {
        if (!this.currentResults || !this.currentResults.detections.length) {
            window.toast?.warning('无数据可下载', '请先运行检测');
            return;
        }

        this.exportResults('json');
    }

    /**
     * Handle share button click
     */
    handleShare() {
        if (!this.currentResults || !this.currentResults.detections.length) {
            window.toast?.warning('无数据可分享', '请先运行检测');
            return;
        }

        // Copy results summary to clipboard
        const summary = this.generateResultsSummary();

        if (window.utils && window.utils.copyToClipboard) {
            window.utils.copyToClipboard(summary).then(success => {
                if (success) {
                    window.toast?.success('已复制', '检测结果摘要已复制到剪贴板');
                } else {
                    window.toast?.error('复制失败', '无法复制到剪贴板');
                }
            });
        } else {
            window.toast?.info('分享功能', '分享功能开发中');
        }
    }

    /**
     * Generate results summary for sharing
     * @returns {string} Results summary
     */
    generateResultsSummary() {
        const { detections, processingTime, model } = this.currentResults;
        const uniqueClasses = new Set(detections.map(d => d.class)).size;

        return `检测结果摘要:
- 检测对象: ${detections.length} 个
- 类别数量: ${uniqueClasses} 种
- 处理时间: ${Math.round(processingTime)} ms
- 使用模型: ${model}

详细结果:
${detections.map((d, i) =>
    `${i + 1}. ${d.class} (${Math.round(d.confidence * 100)}%)`
).join('\n')}`;
    }



    /**
     * Clear all highlights
     */
    clearHighlights() {
        const highlightedRows = this.resultsTableBody.querySelectorAll('tr.highlighted');
        highlightedRows.forEach(row => {
            row.classList.remove('highlighted');
        });
    }

    /**
     * Highlight detection on image
     * @param {number} detectionId - Detection ID
     * @param {number} index - Detection index
     */
    highlightDetection(detectionId, index) {
        // Interact with image display component to highlight detection
        if (window.imageDisplay) {
            window.imageDisplay.highlightDetection(index);
        }

        // Also dispatch an event for other components
        const event = new CustomEvent('highlightDetection', {
            detail: { detectionId, index }
        });
        document.dispatchEvent(event);
    }

    /**
     * Handle detection highlighted from image display
     * @param {number} index - Detection index
     */
    handleDetectionHighlighted(index) {
        // Clear previous highlights
        this.clearHighlights();

        // Find and highlight the corresponding row
        const rows = this.resultsTableBody.querySelectorAll('tr');
        if (rows[index] && !rows[index].classList.contains('no-results-row')) {
            rows[index].classList.add('highlighted');

            // Scroll the row into view
            rows[index].scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }
    }

    /**
     * Handle detection highlighted from image display
     * @param {number} index - Detection index
     */
    handleDetectionHighlighted(index) {
        // Clear previous highlights
        this.clearHighlights();

        // Find and highlight the corresponding row
        const rows = this.resultsTableBody.querySelectorAll('tr');
        if (rows[index] && !rows[index].classList.contains('no-results-row')) {
            rows[index].classList.add('highlighted');

            // Scroll the row into view
            rows[index].scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }
    }

    /**
     * Show detection details in a tooltip or modal
     * @param {number} detectionId - Detection ID
     */
    showDetectionDetails(detectionId) {
        if (!this.currentResults) return;
        
        const detection = this.currentResults.detections.find(d => d.id === detectionId);
        if (!detection) return;
        
        const { class: className, confidence, bbox, area } = detection;
        const [x, y, width, height] = bbox;
        
        const details = `
            类别: ${className}
            置信度: ${(confidence * 100).toFixed(1)}%
            边界框: (${Math.round(x)}, ${Math.round(y)}, ${Math.round(width)}, ${Math.round(height)})
            ${area ? `面积: ${Math.round(area)} 像素` : ''}
        `;
        
        window.toast.info('检测详情', details);
    }

    /**
     * Show no results message
     */
    showNoResults() {
        this.resultsTableBody.innerHTML = `
            <tr class="no-results-row">
                <td colspan="3" class="no-results-cell">暂无检测结果</td>
            </tr>
        `;

        // Reset statistics
        this.objectsCount.textContent = '0';
        this.processingTimeDisplay.textContent = '0 ms';
        this.classesCount.textContent = '0';
    }

    /**
     * Clear all results
     */
    clearResults() {
        this.currentResults = null;
        this.showNoResults();
    }

    /**
     * Export results to different formats
     * @param {string} format - Export format ('json', 'csv', 'txt')
     */
    exportResults(format = 'json') {
        if (!this.currentResults || !this.currentResults.detections.length) {
            window.toast.warning('无数据可导出', '请先运行检测');
            return;
        }

        let content, filename, mimeType;
        
        switch (format) {
            case 'csv':
                content = this.generateCSV();
                filename = `detection_results_${Date.now()}.csv`;
                mimeType = 'text/csv';
                break;
            case 'txt':
                content = this.generateTXT();
                filename = `detection_results_${Date.now()}.txt`;
                mimeType = 'text/plain';
                break;
            default:
                content = JSON.stringify(this.currentResults, null, 2);
                filename = `detection_results_${Date.now()}.json`;
                mimeType = 'application/json';
        }
        
        this.downloadFile(content, filename, mimeType);
        window.toast.success('导出成功', `结果已导出为 ${format.toUpperCase()} 格式`);
    }

    /**
     * Generate CSV format
     * @returns {string} CSV content
     */
    generateCSV() {
        const headers = ['ID', 'Class', 'Confidence', 'X', 'Y', 'Width', 'Height', 'Area'];
        const rows = [headers.join(',')];
        
        this.currentResults.detections.forEach(detection => {
            const { id, class: className, confidence, bbox, area } = detection;
            const [x, y, width, height] = bbox;
            const row = [
                id,
                `"${className}"`,
                confidence.toFixed(3),
                Math.round(x),
                Math.round(y),
                Math.round(width),
                Math.round(height),
                area ? Math.round(area) : ''
            ];
            rows.push(row.join(','));
        });
        
        return rows.join('\n');
    }

    /**
     * Generate TXT format
     * @returns {string} TXT content
     */
    generateTXT() {
        const lines = [
            '检测结果报告',
            '=' * 50,
            `时间: ${new Date(this.currentResults.timestamp || Date.now()).toLocaleString()}`,
            `模型: ${this.currentResults.model}`,
            `处理时间: ${Math.round(this.currentResults.processingTime)}ms`,
            `检测对象数量: ${this.currentResults.detections.length}`,
            '',
            '检测详情:',
            '-' * 30
        ];
        
        this.currentResults.detections.forEach((detection, index) => {
            const { class: className, confidence, bbox, area } = detection;
            const [x, y, width, height] = bbox;
            
            lines.push(`${index + 1}. ${className}`);
            lines.push(`   置信度: ${(confidence * 100).toFixed(1)}%`);
            lines.push(`   位置: (${Math.round(x)}, ${Math.round(y)})`);
            lines.push(`   尺寸: ${Math.round(width)} × ${Math.round(height)}`);
            if (area) {
                lines.push(`   面积: ${Math.round(area)} 像素`);
            }
            lines.push('');
        });
        
        return lines.join('\n');
    }

    /**
     * Download file
     * @param {string} content - File content
     * @param {string} filename - File name
     * @param {string} mimeType - MIME type
     */
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
    }

    /**
     * Get current results
     * @returns {Object} Current results
     */
    getCurrentResults() {
        return this.currentResults;
    }

    /**
     * Filter results by class
     * @param {string} className - Class name to filter by
     */
    filterByClass(className) {
        if (!this.currentResults) return;
        
        const filteredDetections = className === 'all' 
            ? this.currentResults.detections
            : this.currentResults.detections.filter(d => d.class === className);
        
        this.renderResults(filteredDetections);
        this.resultsCount.textContent = `${filteredDetections.length} 个对象`;
    }
}

// Create global results panel instance
window.resultsPanel = new ResultsPanel();
