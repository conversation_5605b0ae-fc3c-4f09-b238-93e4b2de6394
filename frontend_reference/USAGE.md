# 托盘状态检测系统 - 使用说明

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）
```bash
cd frontend_reference
chmod +x start.sh
./start.sh
```

### 方法二：手动启动
```bash
cd frontend_reference

# 同步文件到 public 目录
mkdir -p public/styles public/services public/components
cp src/styles/* public/styles/
cp src/services/* public/services/
cp src/components/* public/components/
cp src/App.js public/
cp src/index.js public/

# 启动服务器
python -m http.server 8080 --directory public
```

## 🌐 访问应用

启动成功后，在浏览器中访问：
- **本地访问**: http://localhost:8080
- **局域网访问**: http://[你的IP地址]:8080

## 📋 功能说明

### 1. 连接状态指示器
- 位于页面右上角
- 显示与后端 WebSocket 的连接状态
- 点击可查看详细连接信息

**状态说明**:
- 🟡 **连接中**: 正在建立连接
- 🟢 **已连接**: 连接正常，可以进行检测
- 🔴 **断开**: 连接断开，需要等待重连
- 🔴 **错误**: 连接出错

### 2. 图像加载
- 点击"加载图像"按钮选择本地图片
- 支持 JPG、PNG、JPEG 格式
- 图像会自动缩放适应显示区域

### 3. 检测功能
- 确保连接状态为"已连接"
- 点击"运行检测"开始检测
- 检测过程中会显示加载动画
- 检测完成后显示结果

### 4. 结果查看
- **图像区域**: 显示检测框和标签
- **结果面板**: 显示详细检测结果列表
- **交互功能**: 点击检测框或结果列表项可高亮显示

### 5. 结果交互
- 点击图像中的检测框 → 高亮对应结果项
- 点击结果列表项 → 高亮对应检测框
- 自动滚动到选中的结果项

## 🔧 故障排除

### 问题1: 页面无法加载
**症状**: 浏览器显示空白页面或 404 错误
**解决方案**:
1. 确保使用了正确的启动方式
2. 检查文件是否正确复制到 public 目录
3. 重新运行启动脚本

### 问题2: 连接状态显示"断开"
**症状**: 连接指示器显示红色"断开"状态
**解决方案**:
1. 确保后端服务正在运行（端口 8000）
2. 检查防火墙设置
3. 确认网络连接正常

### 问题3: 检测功能无响应
**症状**: 点击"运行检测"后无反应
**解决方案**:
1. 检查连接状态是否为"已连接"
2. 确保已加载图像
3. 查看浏览器控制台是否有错误信息

### 问题4: 检测结果不显示
**症状**: 检测完成但看不到结果
**解决方案**:
1. 切换到"检测结果"标签页
2. 检查图像是否包含可检测的对象
3. 尝试调整检测参数

## 🔍 开发调试

### 查看控制台日志
1. 打开浏览器开发者工具（F12）
2. 切换到 Console 标签
3. 查看错误信息和调试日志

### 常见错误信息
- `WebSocket connection failed`: WebSocket 连接失败
- `Failed to load resource`: 资源文件加载失败
- `TypeError: Cannot read property`: JavaScript 代码错误

## 📁 文件结构

```
frontend_reference/
├── public/                 # 部署目录
│   ├── index.html         # 主页面
│   ├── styles/            # 样式文件
│   ├── services/          # 服务文件
│   ├── components/        # 组件文件
│   ├── App.js            # 主应用
│   └── index.js          # 入口文件
├── src/                   # 源代码目录
│   ├── styles/           # 原始样式文件
│   ├── services/         # 原始服务文件
│   ├── components/       # 原始组件文件
│   ├── App.js           # 原始主应用
│   └── index.js         # 原始入口文件
├── start.sh             # 启动脚本
├── USAGE.md            # 使用说明（本文件）
└── MIGRATION_SUMMARY.md # 迁移总结
```

## 🎯 使用技巧

1. **最佳体验**: 使用现代浏览器（Chrome、Firefox、Safari、Edge）
2. **网络要求**: 确保前端和后端在同一网络环境中
3. **图像选择**: 选择清晰、光线良好的托盘图像
4. **结果查看**: 利用交互功能快速定位检测结果
5. **状态监控**: 随时关注连接状态指示器

## 📞 技术支持

如果遇到问题，请：
1. 查看本使用说明的故障排除部分
2. 检查浏览器控制台的错误信息
3. 确认后端服务是否正常运行
4. 检查网络连接和防火墙设置

---

**版本**: 1.0.0  
**更新日期**: 2025-08-10  
**兼容性**: 现代浏览器，Python 3.6+
