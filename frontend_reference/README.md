# 图像检测前端应用

基于 HTML + JavaScript 的图像检测前端应用，提供直观的用户界面用于图像目标检测。

## 项目结构

```
frontend/
├── public/                   # 公共文件
│   └── index.html           # 主 HTML 文件
├── src/                     # 源代码
│   ├── assets/              # 静态资源（图片、图标等）
│   ├── components/          # UI 组件
│   │   ├── ImageDisplay.js  # 图像显示组件
│   │   ├── ControlPanel.js  # 控制面板组件
│   │   ├── ResultsPanel.js  # 结果面板组件
│   │   └── Toast.js         # 通知组件
│   ├── services/            # 服务模块
│   │   ├── api.js          # API 通信服务
│   │   └── websocket.js    # WebSocket 服务
│   ├── styles/              # 样式文件
│   │   ├── main.css        # 主样式
│   │   ├── layout.css      # 布局样式
│   │   └── components.css  # 组件样式
│   ├── App.js              # 主应用组件
│   └── index.js            # 应用入口
├── scripts/                 # 构建脚本
│   └── build.js            # 构建脚本
└── package.json            # 项目配置
```

## 功能特性

- 📸 **图像加载**: 支持本地文件上传和演示图像
- 🤖 **模型选择**: 支持多种检测模型（YOLO v8、Faster R-CNN 等）
- ⚙️ **参数调节**: 可调节置信度阈值和 NMS 阈值
- 🎯 **实时检测**: 显示检测结果和边界框
- 📊 **结果展示**: 详细的检测结果列表和统计信息
- 💾 **结果导出**: 支持 JSON、CSV、TXT 格式导出
- 🔄 **实时通信**: WebSocket 支持实时状态更新
- 📱 **响应式设计**: 适配不同屏幕尺寸
- ⌨️ **快捷键支持**: 提供常用操作快捷键

## 快速开始

### 安装依赖

```bash
cd frontend
npm install
```

### 开发模式

```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动。

### 构建生产版本

```bash
npm run build
```

构建结果将输出到 `dist/` 目录。

### 部署

```bash
npm run serve
```

启动生产服务器。

## 使用说明

### 基本操作

1. **加载图像**: 点击"加载图像"按钮，选择本地图像文件或使用演示图像
2. **选择模型**: 在模型选择下拉框中选择检测模型
3. **调整参数**: 使用滑块调整置信度阈值和 NMS 阈值
4. **运行检测**: 点击"运行检测"按钮开始检测
5. **查看结果**: 在"检测结果"标签页查看详细结果
6. **保存结果**: 点击"保存结果"按钮导出检测结果

### 快捷键

- `Ctrl+O`: 加载图像
- `Ctrl+R`: 运行检测
- `Ctrl+S`: 保存结果
- `Tab`: 切换标签页

### API 集成

应用支持与后端 API 集成，默认配置：

- **HTTP API**: `http://localhost:8000/api`
- **WebSocket**: `ws://localhost:8000/ws`

如果后端不可用，应用将自动切换到演示模式。

## 技术栈

- **HTML5**: 语义化标记
- **CSS3**: 现代样式和动画
- **JavaScript (ES6+)**: 原生 JavaScript，无框架依赖
- **WebSocket**: 实时通信
- **Canvas API**: 检测结果可视化

## 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发指南

### 添加新组件

1. 在 `src/components/` 目录创建新的 JS 文件
2. 实现组件类，遵循现有组件的模式
3. 在 `index.html` 中引入组件脚本
4. 在 `App.js` 中初始化组件

### 添加新样式

1. 在 `src/styles/` 目录添加 CSS 文件
2. 在 `index.html` 中引入样式文件
3. 遵循 BEM 命名规范

### API 扩展

1. 在 `src/services/api.js` 中添加新的 API 方法
2. 处理错误和加载状态
3. 更新相关组件以使用新 API

## 配置选项

### 环境变量

可以通过修改相关文件来配置：

- API 基础 URL: 在 `src/services/api.js` 中修改 `getBaseUrl()` 方法
- WebSocket URL: 在 `src/services/websocket.js` 中修改 `getWebSocketUrl()` 方法

### 自定义主题

修改 `src/styles/main.css` 中的 CSS 变量来自定义主题颜色。

## 故障排除

### 常见问题

1. **图像无法加载**: 检查文件格式和大小限制
2. **检测失败**: 确认后端 API 可用性
3. **WebSocket 连接失败**: 检查网络连接和后端服务状态

### 调试模式

在浏览器开发者工具中查看控制台日志获取详细错误信息。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
