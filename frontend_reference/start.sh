#!/bin/bash

# 图像检测前端应用启动脚本

echo "🚀 启动图像检测前端应用..."

# 检查 Python 是否可用
if command -v python3 &> /dev/null; then
    echo "✅ 使用 Python3 启动 HTTP 服务器"
    echo "📱 应用地址: http://localhost:3000/public/"
    echo "⏹️  按 Ctrl+C 停止服务器"
    echo ""
    python3 -m http.server 3000
elif command -v python &> /dev/null; then
    echo "✅ 使用 Python 启动 HTTP 服务器"
    echo "📱 应用地址: http://localhost:3000/public/"
    echo "⏹️  按 Ctrl+C 停止服务器"
    echo ""
    python -m http.server 3000
elif command -v node &> /dev/null; then
    echo "✅ 使用 Node.js 启动 HTTP 服务器"
    echo "📱 应用地址: http://localhost:3000/public/"
    echo "⏹️  按 Ctrl+C 停止服务器"
    echo ""
    npx http-server . -p 3000 -o
else
    echo "❌ 未找到 Python 或 Node.js"
    echo "请安装 Python 3 或 Node.js 来运行开发服务器"
    exit 1
fi
