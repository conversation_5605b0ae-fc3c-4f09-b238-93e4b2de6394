#!/bin/bash

# 托盘状态检测系统启动脚本

echo "🚀 启动托盘状态检测系统..."

# 确保 public 目录中有最新的文件
echo "📁 同步文件到 public 目录..."
mkdir -p public/styles public/services public/components

# 复制样式文件
cp src/styles/* public/styles/ 2>/dev/null || true

# 复制服务文件
cp src/services/* public/services/ 2>/dev/null || true

# 复制组件文件
cp src/components/* public/components/ 2>/dev/null || true

# 复制主要 JS 文件
cp src/App.js public/ 2>/dev/null || true
cp src/index.js public/ 2>/dev/null || true

echo "✅ 文件同步完成"

# 检查 Python 是否可用
if command -v python3 &> /dev/null; then
    echo "✅ 使用 Python3 启动 HTTP 服务器"
    echo "📱 应用地址: http://localhost:8080"
    echo "⏹️  按 Ctrl+C 停止服务器"
    echo ""
    python3 -m http.server 8080 --directory public
elif command -v python &> /dev/null; then
    echo "✅ 使用 Python 启动 HTTP 服务器"
    echo "📱 应用地址: http://localhost:8080"
    echo "⏹️  按 Ctrl+C 停止服务器"
    echo ""
    cd public && python -m http.server 8080
else
    echo "❌ 未找到 Python"
    echo "请安装 Python 3 来运行开发服务器"
    exit 1
fi
