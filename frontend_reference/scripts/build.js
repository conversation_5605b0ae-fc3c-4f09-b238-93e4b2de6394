#!/usr/bin/env node

/**
 * Simple build script for HTML + JS frontend
 * Copies files and processes them for production
 */

const fs = require('fs');
const path = require('path');

const srcDir = path.join(__dirname, '../src');
const publicDir = path.join(__dirname, '../public');
const distDir = path.join(__dirname, '../dist');

/**
 * Copy directory recursively
 * @param {string} src - Source directory
 * @param {string} dest - Destination directory
 */
function copyDir(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
            copyDir(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

/**
 * Process HTML file to update paths for production
 * @param {string} filePath - HTML file path
 */
function processHtmlFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Update CSS paths
    content = content.replace(/\.\.\/src\/styles\//g, './styles/');
    
    // Update JS paths
    content = content.replace(/\.\.\/src\//g, './');
    
    fs.writeFileSync(filePath, content);
}

/**
 * Main build function
 */
function build() {
    console.log('Starting build process...');
    
    try {
        // Clean dist directory
        if (fs.existsSync(distDir)) {
            fs.rmSync(distDir, { recursive: true, force: true });
        }
        fs.mkdirSync(distDir, { recursive: true });
        
        // Copy public files
        console.log('Copying public files...');
        copyDir(publicDir, distDir);
        
        // Copy src files
        console.log('Copying source files...');
        copyDir(srcDir, distDir);
        
        // Process HTML files
        console.log('Processing HTML files...');
        const htmlFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.html'));
        htmlFiles.forEach(file => {
            processHtmlFile(path.join(distDir, file));
        });
        
        console.log('Build completed successfully!');
        console.log(`Output directory: ${distDir}`);
        
    } catch (error) {
        console.error('Build failed:', error);
        process.exit(1);
    }
}

// Run build if this script is executed directly
if (require.main === module) {
    build();
}

module.exports = { build };
