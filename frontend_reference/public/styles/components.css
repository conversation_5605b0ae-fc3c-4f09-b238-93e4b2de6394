/* Panel Styles */
.panel {
    background-color: var(--card);
    border-radius: var(--radius);
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
}

.panel-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--foreground);
}

/* Model Panel */
.model-panel {
    margin-bottom: 20px;
}

/* Tabs */
.tabs-container {
    flex: 1;
}

.tabs-header {
    display: flex;
    gap: 8px; /* Add spacing between tab buttons */
    background-color: var(--card);
    border-radius: var(--radius) var(--radius) 0 0;
    border: 1px solid var(--border);
    border-bottom: none;
    box-shadow: var(--shadow-sm);
    padding: 4px; /* Add padding to accommodate the gap */
}

.tab-btn {
    flex: 1;
    padding: 16px 20px;
    background: var(--muted); /* Slightly gray background for inactive tabs */
    border: none;
    color: var(--muted-foreground);
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: var(--radius); /* Full rounded corners for individual buttons */
    min-height: 52px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1); /* Subtle inset shadow for inactive state */
}

/* Remove border separator since we now have gap spacing */

.tab-btn.active {
    background-color: var(--background); /* Bright background for active state */
    color: var(--foreground); /* Strong text color for active state */
    font-weight: 600; /* Bolder text for active state */
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1), /* Outer shadow for elevation */
        inset 0 1px 0 rgba(255, 255, 255, 0.2), /* Top highlight for embossed effect */
        inset 0 -1px 0 rgba(0, 0, 0, 0.1); /* Bottom shadow for embossed effect */
    transform: translateY(-1px); /* Slight lift for active state */
}

.tab-btn:hover:not(.active) {
    background-color: var(--secondary); /* Lighter background on hover */
    color: var(--foreground);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05); /* Reduced inset shadow on hover */
    transform: translateY(-0.5px); /* Slight lift on hover */
}

/* Remove default focus outline for tab buttons since we have strong visual contrast */
.tab-btn:focus,
.tab-btn:focus-visible {
    outline: none; /* Remove blue border outline */
}

.tab-content {
    display: none;
    background-color: var(--card);
    border: 1px solid var(--border);
    border-top: none;
    border-radius: 0 0 var(--radius) var(--radius);
    padding: 16px;
    box-shadow: var(--shadow);
}

.tab-content.active {
    display: block;
}

/* Control Panel */
.control-panel {
    background-color: var(--card);
    border-radius: var(--radius);
    padding: 20px;
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
}

/* Control Actions */
.control-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 24px;
}

/* Individual button spacing will be added to the main .action-btn rule below */

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    border-radius: var(--radius);
    font-size: 15px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid var(--primary);
    background: var(--background);
    color: var(--primary);
    box-shadow: var(--shadow-sm);
    /* Keep tablet-friendly sizing */
    padding: 16px 20px;
    min-height: 52px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    /* Add spacing between buttons */
    margin: 8px;

}

.action-btn:hover:not(:disabled) {
    background-color: var(--primary);
    color: var(--primary-foreground);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.action-btn:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
}

.action-btn:disabled {
    pointer-events: none;
    opacity: 0.5;
}

/* Button Icon Styles */
.action-btn .btn-icon {
    font-size: 16px;
    line-height: 1;
    flex-shrink: 0;
}

/* SVG icons in buttons */
.action-btn svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.action-btn:disabled {
    border-color: var(--border);
    color: var(--muted-foreground);
    cursor: not-allowed;
    transform: none;
    opacity: 0.5;
}

.action-btn .btn-icon {
    font-size: 16px;
}

/* All action buttons now use the same primary style */

/* Settings button now uses the same unified style as other action buttons */

/* Control Divider */
.control-divider {
    height: 1px;
    background-color: var(--border);
    margin: 20px 0;
}

/* Control Groups */
.control-group {
    margin-bottom: 20px;
}

.control-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--foreground);
    margin-bottom: 8px;
}

/* Slider Group */
.slider-group {
    margin-bottom: 24px;
}

.slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.slider-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary);
    min-width: 40px;
    text-align: right;
}

.slider-container {
    position: relative;
}

.modern-slider {
    width: 100%;
    height: 6px; /* h-1.5 equivalent */
    border-radius: 9999px; /* rounded-full */
    background: color-mix(in srgb, var(--primary) 20%, transparent); /* bg-primary/20 */
    outline: none;
    -webkit-appearance: none;
    position: relative;
    touch-action: manipulation;
    cursor: pointer;
    overflow: hidden;
}

.modern-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px; /* Larger thumb for better usability */
    height: 20px;
    border-radius: 50%; /* Perfect circle */
    background: var(--background); /* bg-background */
    cursor: pointer;
    border: 2px solid var(--primary); /* Stronger border with primary color */
    box-shadow: var(--shadow); /* shadow */
    transition: all 0.2s ease;
    touch-action: manipulation;
}

.modern-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--background);
    cursor: pointer;
    border: 2px solid var(--primary);
    box-shadow: var(--shadow);
}

.modern-slider::-webkit-slider-track {
    background: transparent; /* Let the slider background show through */
    height: 6px;
    border-radius: 9999px;
}

/* Create the filled range effect using a pseudo-element */
.modern-slider::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: var(--slider-progress, 65%); /* Use CSS variable for dynamic width */
    background: var(--primary); /* bg-primary for the filled portion */
    border-radius: 9999px;
    pointer-events: none;
    transition: width 0.1s ease;
}

/* Toggle Switch */
.toggle-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--muted);
    border: 1px solid var(--border);
    border-radius: 24px;
    transition: 0.3s;
}

.toggle-label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: var(--card);
    border-radius: 50%;
    transition: 0.3s;
    box-shadow: var(--shadow-sm);
}

.toggle-input:checked + .toggle-label {
    background-color: var(--primary);
    border-color: var(--primary);
}

.toggle-input:checked + .toggle-label:before {
    transform: translateX(26px);
}

/* Section Title */
.section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 16px;
}

/* Detection Classes */
.classes-group {
    margin-bottom: 24px;
}

.classes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.class-item {
    position: relative;
}

.class-checkbox {
    display: none;
}

.class-label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 15px;
    color: var(--foreground);
    background-color: var(--background);
    border: 1px solid var(--border);
    min-height: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    box-shadow: var(--shadow-sm);
}

.class-label:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.class-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--primary);
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.class-dot-green {
    background-color: var(--pallet-empty) !important;
}

.class-dot-yellow {
    background-color: var(--pallet-loaded) !important;
}

.class-checkbox:checked + .class-label {
    background-color: var(--secondary);
    border-color: var(--primary);
    box-shadow: var(--shadow);
}

/* Green pallet styling */
#class-pallet:checked + .class-label {
    background-color: color-mix(in srgb, var(--pallet-empty) 10%, var(--background));
    border-color: var(--pallet-empty);
    box-shadow: 0 0 0 1px var(--pallet-empty);
}

/* Purple loaded pallet styling */
#class-non-empty-pallet:checked + .class-label {
    background-color: color-mix(in srgb, var(--pallet-loaded) 10%, var(--background));
    border-color: var(--pallet-loaded);
    box-shadow: 0 0 0 1px var(--pallet-loaded);
}

.class-checkbox:not(:checked) + .class-label {
    opacity: 0.5;
}

.class-checkbox:not(:checked) + .class-label .class-dot {
    background-color: var(--muted-foreground) !important;
}

/* Responsive Design for Control Panel */

/* Tablet Landscape (1024px and below) */
@media (max-width: 1024px) {
    .control-actions {
        gap: 14px;
    }

    .action-btn {
        min-height: 50px;
        font-size: 15px;
        padding: 14px 18px;
    }

    .classes-grid {
        gap: 12px;
    }

    .class-label {
        min-height: 50px;
        padding: 14px 18px;
    }

    .modern-slider::-webkit-slider-thumb {
        width: 28px;
        height: 28px;
    }
}

/* Tablet Portrait (768px and below) */
@media (max-width: 768px) {
    .control-actions {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .action-btn {
        min-height: 52px;
        font-size: 16px;
        padding: 16px 20px;
    }

    .classes-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .class-label {
        min-height: 52px;
        padding: 16px 20px;
        font-size: 16px;
    }

    .control-panel {
        padding: 16px;
    }

    .control-divider {
        margin: 20px 0;
    }

    .modern-slider {
        height: 10px;
    }

    .modern-slider::-webkit-slider-thumb {
        width: 30px;
        height: 30px;
    }

    .slider-container {
        padding: 8px 0;
    }
}

/* Mobile (480px and below) */
@media (max-width: 480px) {
    .action-btn {
        min-height: 48px;
        font-size: 15px;
        padding: 14px 18px;
    }

    .class-label {
        min-height: 48px;
        padding: 14px 16px;
        font-size: 15px;
    }

    .control-panel {
        padding: 12px;
    }
}

/* Enhanced Button Animations */
.action-btn {
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

/* Improved Focus States */
.action-btn:focus,
.modern-slider:focus,
.toggle-input:focus + .toggle-label,
.class-checkbox:focus + .class-label {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
}

/* Loading State for Buttons */
.action-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.action-btn.loading .btn-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced Slider Styling */
.modern-slider:hover::-webkit-slider-thumb {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.5);
}

.modern-slider:active::-webkit-slider-thumb {
    transform: scale(1.2);
}

/* Tooltip for Slider Value */
.slider-container {
    position: relative;
}

.slider-tooltip {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--popover);
    color: var(--popover-foreground);
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
}

.modern-slider:hover + .slider-tooltip,
.modern-slider:focus + .slider-tooltip {
    opacity: 1;
}

/* Results Panel */
.results-panel {
    background-color: var(--card);
    border-radius: var(--radius);
    padding: 20px;
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
}

/* Results Header */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.results-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.results-actions {
    display: flex;
    gap: 8px;
}

.results-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 6px;
    background-color: #555;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.results-action-btn:hover {
    background-color: #666;
    transform: translateY(-1px);
}

.action-icon {
    font-size: 16px;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background-color: var(--background);
    border-radius: var(--radius);
    padding: 16px;
    text-align: left;
    border: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
}

.stat-label {
    font-size: 14px;
    color: var(--muted-foreground);
    margin-bottom: 8px;
    display: block;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--foreground);
    line-height: 1;
}

/* Results Table */
.results-table-container {
    background-color: var(--card);
    border-radius: var(--radius);
    overflow: hidden;
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.results-table th {
    background-color: var(--muted);
    color: var(--foreground);
    font-weight: 600;
    padding: 18px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border);
    font-size: 15px;
}

.results-table td {
    padding: 20px 16px;
    border-bottom: 1px solid var(--border);
    color: var(--foreground);
    font-size: 14px;
}

.results-table tbody tr:last-child td {
    border-bottom: none;
}

.results-table tbody tr:hover {
    background-color: var(--muted);
    cursor: pointer;
}

.results-table tbody tr {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.results-table tbody tr.highlighted {
    background-color: color-mix(in srgb, var(--primary) 10%, var(--background));
    border-left: 3px solid var(--primary);
}

/* Pallet-specific highlighting */
.results-table tbody tr.highlighted.pallet-row {
    background-color: color-mix(in srgb, var(--pallet-empty) 15%, var(--background));
    border-left: 3px solid var(--pallet-empty);
}

.results-table tbody tr.highlighted.non-empty-pallet-row {
    background-color: color-mix(in srgb, var(--pallet-loaded) 15%, var(--background));
    border-left: 3px solid var(--pallet-loaded);
}

/* Table Columns */
.col-class {
    width: 30%;
}

.col-confidence {
    width: 25%;
}

.col-position {
    width: 45%;
}

/* Confidence Display */
.confidence-value {
    font-weight: 600;
    color: var(--chart-5);
}

/* Position Display */
.position-value {
    font-family: 'Courier New', monospace;
    color: var(--muted-foreground);
    font-size: 13px;
}

/* No Results */
.no-results-row {
    background-color: transparent !important;
}

.no-results-cell {
    text-align: center;
    color: #888;
    font-style: italic;
    padding: 40px 20px !important;
    border-bottom: none !important;
}

/* Class Name Styling */
.class-name {
    font-weight: 500;
    color: var(--foreground);
}

/* Responsive Design for Results Panel */

/* Tablet Landscape (1024px and below) */
@media (max-width: 1024px) {
    .results-action-btn {
        width: 44px;
        height: 44px;
        touch-action: manipulation;
    }

    .results-table th,
    .results-table td {
        padding: 18px 14px;
        font-size: 15px;
    }
}

/* Tablet Portrait (768px and below) */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .stat-card {
        padding: 16px;
        touch-action: manipulation;
    }

    .stat-value {
        font-size: 28px;
    }

    .results-table th,
    .results-table td {
        padding: 20px 12px;
        font-size: 15px;
    }

    .col-class {
        width: 30%;
    }

    .col-confidence {
        width: 25%;
    }

    .col-position {
        width: 45%;
    }

    .position-value {
        font-size: 13px;
    }

    .results-title {
        font-size: 19px;
    }

    .results-actions {
        gap: 8px;
    }

    .results-action-btn {
        width: 48px;
        height: 48px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
    }

    .stat-card:last-child {
        grid-column: 1 / -1;
    }

    .results-table {
        font-size: 12px;
    }

    .results-table th,
    .results-table td {
        padding: 8px 6px;
    }

    .position-value {
        font-size: 10px;
        word-break: break-all;
    }
}

/* =====================================
 * Tech-inspired Components
 * ===================================== */

/* Tech Card Component */
.tech-card {
    background-color: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.2s ease;
}

.tech-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Tech Gradient Backgrounds */
.tech-gradient-blue {
    background: linear-gradient(135deg, var(--primary) 0%, var(--chart-3) 100%);
    color: var(--primary-foreground);
}

.tech-gradient-purple {
    background: linear-gradient(135deg, var(--accent) 0%, var(--chart-4) 100%);
    color: var(--accent-foreground);
}

/* Tech Badge Component */
.tech-badge {
    display: inline-flex;
    align-items: center;
    border-radius: calc(var(--radius) - 2px);
    border: 1px solid transparent;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.tech-badge.primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
}

.tech-badge.secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
}

.tech-badge.success {
    background-color: var(--chart-5);
    color: var(--primary-foreground);
}

.tech-badge.warning {
    background-color: var(--pallet-loaded);
    color: var(--primary-foreground);
}

.tech-badge.outline {
    background-color: transparent;
    border-color: var(--border);
    color: var(--foreground);
}

/* Enhanced Action Buttons */
.results-action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius);
    background-color: var(--background);
    color: var(--foreground);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.results-action-btn:hover {
    background-color: var(--accent);
    color: var(--accent-foreground);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Enhanced Focus States */
.tech-focus:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--background), 0 0 0 4px var(--ring);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.online {
    background-color: color-mix(in srgb, var(--chart-5) 10%, var(--background));
    color: var(--chart-5);
    border: 1px solid var(--chart-5);
}

.status-indicator.processing {
    background-color: color-mix(in srgb, var(--primary) 10%, var(--background));
    color: var(--primary);
    border: 1px solid var(--primary);
}

.status-indicator.error {
    background-color: color-mix(in srgb, var(--destructive) 10%, var(--background));
    color: var(--destructive);
    border: 1px solid var(--destructive);
}

.status-indicator::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: currentColor;
}

/* Connection Status Indicator */
.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--border);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

/* Connection states */
.connection-status.connecting .status-dot {
    background: #ffc107;
    animation: pulse 2s infinite;
}

.connection-status.connected .status-dot {
    background: #28a745;
    animation: none;
}

.connection-status.disconnected .status-dot {
    background: #dc3545;
    animation: none;
}

.connection-status.error .status-dot {
    background: #dc3545;
    animation: none;
}

.status-text {
    color: var(--foreground);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Pulse animation for connecting state */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Modern Scrollbar */
.tech-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.tech-scrollbar::-webkit-scrollbar-track {
    background: var(--muted);
    border-radius: var(--radius);
}

.tech-scrollbar::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: var(--radius);
    border: 2px solid var(--muted);
}

.tech-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--foreground);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.toast {
    background-color: var(--card);
    color: var(--card-foreground);
    padding: 12px 16px;
    border-radius: var(--radius);
    border-left: 4px solid var(--primary);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border);
    min-width: 300px;
    max-width: 400px;
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    border-left-color: #28a745;
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.warning {
    border-left-color: #ffc107;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.toast-description {
    font-size: 14px;
    color: #ccc;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Result item highlighting */
.result-item.highlighted {
    background-color: #007bff;
    border-left-color: #ffc107;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.result-item:hover {
    background-color: #555;
    cursor: pointer;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

body.loading {
    cursor: wait;
}

/* Additional utility classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}
