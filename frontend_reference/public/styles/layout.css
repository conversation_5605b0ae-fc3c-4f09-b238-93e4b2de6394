/* Header Styles */
.header {
    background-color: var(--sidebar-background);
    border-bottom: 1px solid var(--sidebar-border);
    padding: 0 16px;
    height: 60px;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.app-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--sidebar-foreground);
}

/* Main Content Layout */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.image-section {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
}

.control-section {
    width: 40%;
    background-color: var(--background);
    padding: 16px;
    overflow-y: auto;
    border-left: 1px solid var(--border);
}

/* Image Display */
.image-display {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--card);
    border-radius: var(--radius);
    border: 2px dashed var(--border);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--muted-foreground);
    font-size: 16px;
}

.main-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
}

.detection-canvas {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: color-mix(in srgb, var(--background) 80%, transparent);
    backdrop-filter: blur(4px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--foreground);
    font-size: 16px;
    border-radius: var(--radius);
}

/* Footer Styles */
.footer {
    background-color: #2d2d2d;
    border-top: 1px solid #444;
    padding: 8px 16px;
    height: 40px;
    flex-shrink: 0;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    font-size: 12px;
}

.status-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-label {
    color: #888;
}

.status-text {
    font-weight: 500;
}

.status-text.ready {
    color: #28a745;
}

.status-text.processing {
    color: #ffc107;
}

.status-text.error {
    color: #dc3545;
}

.system-text {
    color: #888;
}
