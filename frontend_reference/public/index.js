/**
 * Application Entry Point
 * Initializes the application when DOM is ready
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing application...');
    
    // Check if all required elements exist
    const requiredElements = [
        'app',
        'header',
        'image-display',
        'toast-container'
    ];
    
    const missingElements = requiredElements.filter(id => !document.getElementById(id));
    
    if (missingElements.length > 0) {
        console.error('Missing required elements:', missingElements);
        showError('页面加载不完整，请刷新页面重试');
        return;
    }
    
    // Initialize application
    try {
        // App is already initialized in App.js
        console.log('Application ready');
        
        // Add loading class to body initially
        document.body.classList.add('loading');
        
        // Remove loading class after initialization
        setTimeout(() => {
            document.body.classList.remove('loading');
        }, 500);
        
    } catch (error) {
        console.error('Application initialization failed:', error);
        showError('应用程序初始化失败，请刷新页面重试');
    }
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('Page hidden');
        // Pause any ongoing operations if needed
    } else {
        console.log('Page visible');
        // Resume operations if needed
        
        // Check WebSocket connection
        if (window.wsService && !window.wsService.isConnected()) {
            console.log('Reconnecting WebSocket...');
            window.wsService.connect();
        }
    }
});

// Handle page unload
window.addEventListener('beforeunload', (event) => {
    // Clean up resources
    if (window.wsService) {
        window.wsService.disconnect();
    }
    
    // Don't show confirmation dialog for now
    // event.preventDefault();
    // event.returnValue = '';
});

// Handle online/offline status
window.addEventListener('online', () => {
    console.log('Network online');
    window.toast?.success('网络连接', '网络连接已恢复');
    
    // Reconnect WebSocket if needed
    if (window.wsService && !window.wsService.isConnected()) {
        window.wsService.connect();
    }
});

window.addEventListener('offline', () => {
    console.log('Network offline');
    window.toast?.warning('网络断开', '网络连接已断开，部分功能可能不可用');
});

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
    // Create a simple error display if toast is not available
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #dc3545;
        color: white;
        padding: 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 400px;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

// Add some utility functions to global scope
window.utils = {
    /**
     * Format file size
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * Format duration
     * @param {number} ms - Duration in milliseconds
     * @returns {string} Formatted duration
     */
    formatDuration(ms) {
        if (ms < 1000) {
            return `${Math.round(ms)}ms`;
        } else if (ms < 60000) {
            return `${(ms / 1000).toFixed(1)}s`;
        } else {
            const minutes = Math.floor(ms / 60000);
            const seconds = ((ms % 60000) / 1000).toFixed(0);
            return `${minutes}:${seconds.padStart(2, '0')}`;
        }
    },
    
    /**
     * Debounce function
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * Throttle function
     * @param {Function} func - Function to throttle
     * @param {number} limit - Time limit in milliseconds
     * @returns {Function} Throttled function
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * Generate unique ID
     * @returns {string} Unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    /**
     * Copy text to clipboard
     * @param {string} text - Text to copy
     * @returns {Promise<boolean>} Success status
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            console.error('Copy to clipboard failed:', error);
            return false;
        }
    }
};

console.log('Application entry point loaded');
