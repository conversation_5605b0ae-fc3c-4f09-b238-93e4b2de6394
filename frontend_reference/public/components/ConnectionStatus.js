/**
 * Connection Status Component
 * Manages WebSocket connection status display
 */
class ConnectionStatus {
    constructor() {
        this.statusElement = document.getElementById('connection-status');
        this.statusDot = this.statusElement.querySelector('.status-dot');
        this.statusText = this.statusElement.querySelector('.status-text');
        
        this.currentStatus = 'connecting';
        
        this.init();
    }

    /**
     * Initialize the connection status component
     */
    init() {
        this.setupEventListeners();
        this.updateStatus('connecting', '连接中...');
    }

    /**
     * Setup event listeners for WebSocket events
     */
    setupEventListeners() {
        // Click handler for showing connection details
        this.statusElement.addEventListener('click', () => {
            this.showConnectionDetails();
        });

        // Add cursor pointer style
        this.statusElement.style.cursor = 'pointer';
        this.statusElement.title = '点击查看连接详情';
        // Listen for WebSocket connection events
        if (window.wsService) {
            window.wsService.on('connected', () => {
                this.updateStatus('connected', '已连接');
            });

            window.wsService.on('disconnected', () => {
                this.updateStatus('disconnected', '连接断开');
            });

            window.wsService.on('error', () => {
                this.updateStatus('error', '连接错误');
            });

            window.wsService.on('maxReconnectAttemptsReached', () => {
                this.updateStatus('error', '连接失败');
            });
        }

        // Listen for network status changes
        window.addEventListener('online', () => {
            if (this.currentStatus === 'disconnected' || this.currentStatus === 'error') {
                this.updateStatus('connecting', '重新连接中...');
                // Trigger reconnection if WebSocket service exists
                if (window.wsService) {
                    setTimeout(() => {
                        window.wsService.connect();
                    }, 1000);
                }
            }
        });

        window.addEventListener('offline', () => {
            this.updateStatus('error', '网络断开');
        });
    }

    /**
     * Update connection status
     * @param {string} status - Status type: 'connecting', 'connected', 'disconnected', 'error'
     * @param {string} text - Status text to display
     */
    updateStatus(status, text) {
        // Remove all status classes
        this.statusElement.classList.remove('connecting', 'connected', 'disconnected', 'error');
        
        // Add new status class
        this.statusElement.classList.add(status);
        
        // Update status text
        this.statusText.textContent = text;
        
        // Store current status
        this.currentStatus = status;

        // Log status change
        console.log(`Connection status changed: ${status} - ${text}`);

        // Dispatch custom event for other components
        const event = new CustomEvent('connectionStatusChanged', {
            detail: { status, text }
        });
        document.dispatchEvent(event);
    }

    /**
     * Get current connection status
     * @returns {string} Current status
     */
    getCurrentStatus() {
        return this.currentStatus;
    }

    /**
     * Check if currently connected
     * @returns {boolean} True if connected
     */
    isConnected() {
        return this.currentStatus === 'connected';
    }

    /**
     * Manually trigger connection attempt
     */
    reconnect() {
        this.updateStatus('connecting', '重新连接中...');
        if (window.wsService) {
            window.wsService.connect();
        }
    }

    /**
     * Show connection details in a tooltip or modal
     */
    showConnectionDetails() {
        const details = this.getConnectionDetails();
        if (window.toast) {
            window.toast.info('连接状态', details);
        } else {
            alert(details);
        }
    }

    /**
     * Get detailed connection information
     * @returns {string} Connection details
     */
    getConnectionDetails() {
        const wsService = window.wsService;
        if (!wsService) {
            return '连接服务未初始化';
        }

        const details = [
            `状态: ${this.getStatusText(this.currentStatus)}`,
            `WebSocket URL: ${wsService.url}`,
            `重连尝试: ${wsService.reconnectAttempts}/${wsService.maxReconnectAttempts}`,
            `网络状态: ${navigator.onLine ? '在线' : '离线'}`
        ];

        if (wsService.ws) {
            details.push(`连接状态: ${this.getReadyStateText(wsService.ws.readyState)}`);
        }

        return details.join('\n');
    }

    /**
     * Get human-readable status text
     * @param {string} status - Status code
     * @returns {string} Human-readable text
     */
    getStatusText(status) {
        const statusMap = {
            'connecting': '连接中',
            'connected': '已连接',
            'disconnected': '已断开',
            'error': '连接错误'
        };
        return statusMap[status] || '未知状态';
    }

    /**
     * Get human-readable WebSocket ready state text
     * @param {number} readyState - WebSocket ready state
     * @returns {string} Human-readable text
     */
    getReadyStateText(readyState) {
        const stateMap = {
            0: '连接中',
            1: '已连接',
            2: '正在关闭',
            3: '已关闭'
        };
        return stateMap[readyState] || '未知状态';
    }
}

// Create global connection status instance
window.connectionStatus = new ConnectionStatus();
