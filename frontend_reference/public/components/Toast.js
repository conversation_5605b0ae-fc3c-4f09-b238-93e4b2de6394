/**
 * Toast notification system
 */
class Toast {
    constructor() {
        this.container = document.getElementById('toast-container');
        this.toasts = new Map();
        this.toastId = 0;
    }

    /**
     * Show a toast notification
     * @param {Object} options - Toast options
     * @param {string} options.title - Toast title
     * @param {string} options.description - Toast description
     * @param {string} options.type - Toast type (success, error, warning, info)
     * @param {number} options.duration - Duration in milliseconds (default: 3000)
     */
    show({ title, description, type = 'info', duration = 3000 }) {
        const id = ++this.toastId;
        
        const toastElement = document.createElement('div');
        toastElement.className = `toast ${type}`;
        toastElement.setAttribute('data-toast-id', id);
        
        toastElement.innerHTML = `
            <div class="toast-title">${title}</div>
            ${description ? `<div class="toast-description">${description}</div>` : ''}
        `;
        
        this.container.appendChild(toastElement);
        this.toasts.set(id, toastElement);
        
        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.remove(id);
            }, duration);
        }
        
        return id;
    }

    /**
     * Remove a toast by ID
     * @param {number} id - Toast ID
     */
    remove(id) {
        const toast = this.toasts.get(id);
        if (toast) {
            toast.style.animation = 'slideOut 0.3s ease-out forwards';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                this.toasts.delete(id);
            }, 300);
        }
    }

    /**
     * Show success toast
     * @param {string} title - Toast title
     * @param {string} description - Toast description
     */
    success(title, description) {
        return this.show({ title, description, type: 'success' });
    }

    /**
     * Show error toast
     * @param {string} title - Toast title
     * @param {string} description - Toast description
     */
    error(title, description) {
        return this.show({ title, description, type: 'error' });
    }

    /**
     * Show warning toast
     * @param {string} title - Toast title
     * @param {string} description - Toast description
     */
    warning(title, description) {
        return this.show({ title, description, type: 'warning' });
    }

    /**
     * Show info toast
     * @param {string} title - Toast title
     * @param {string} description - Toast description
     */
    info(title, description) {
        return this.show({ title, description, type: 'info' });
    }

    /**
     * Clear all toasts
     */
    clear() {
        this.toasts.forEach((toast, id) => {
            this.remove(id);
        });
    }
}

// Create global toast instance
window.toast = new Toast();
