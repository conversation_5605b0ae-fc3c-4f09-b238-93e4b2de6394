/**
 * WebSocket Service
 * Handles real-time communication with backend
 */
class WebSocketService {
    constructor() {
        this.ws = null;
        this.url = this.getWebSocketUrl();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.isConnecting = false;
        this.eventListeners = new Map();
        this.heartbeatInterval = null;
        this.heartbeatDelay = 30000; // 30 seconds

        this.connect();
    }

    /**
     * Get WebSocket URL
     * @returns {string} WebSocket URL
     */
    getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const hostname = window.location.hostname;
        const port = window.location.port;
        
        // Default to same origin with WebSocket path
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return `${protocol}//${hostname}:8000/ws`; // Assuming backend runs on port 8000
        }
        
        return `${protocol}//${hostname}${port ? ':' + port : ''}/ws`;
    }

    /**
     * Connect to WebSocket server
     */
    connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
            return;
        }

        this.isConnecting = true;
        
        try {
            this.ws = new WebSocket(this.url);
            this.setupEventHandlers();
        } catch (error) {
            console.error('WebSocket connection error:', error);
            this.isConnecting = false;
            this.scheduleReconnect();
        }
    }

    /**
     * Setup WebSocket event handlers
     */
    setupEventHandlers() {
        this.ws.onopen = (event) => {
            console.log('WebSocket connected');
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            this.emit('connected', event);
        };

        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            this.isConnecting = false;
            this.stopHeartbeat();
            this.emit('disconnected', event);

            if (!event.wasClean) {
                this.scheduleReconnect();
            }
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.isConnecting = false;
            this.emit('error', error);
        };
    }

    /**
     * Handle incoming WebSocket messages
     * @param {Object} data - Message data
     */
    handleMessage(data) {
        const { type, payload } = data;

        switch (type) {
            // Original backend protocol messages
            case 'detection_started':
                this.emit('detectionStarted', data);
                break;
            case 'detection_result':
                this.emit('detectionResult', data);
                break;
            case 'error':
                this.emit('error', data);
                break;
            case 'pong':
                this.emit('pong', data);
                break;

            // New protocol messages (for future compatibility)
            case 'detection_progress':
                this.emit('detectionProgress', payload);
                break;
            case 'detection_complete':
                this.emit('detectionComplete', payload);
                break;
            case 'detection_error':
                this.emit('detectionError', payload);
                break;
            case 'system_status':
                this.emit('systemStatus', payload);
                break;
            case 'model_loaded':
                this.emit('modelLoaded', payload);
                break;
            default:
                this.emit('message', data);
        }
    }

    /**
     * Send message to WebSocket server
     * @param {Object} data - Message data
     */
    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        } else {
            console.warn('WebSocket not connected, message not sent:', data);
        }
    }

    /**
     * Send detection request
     * @param {Object} params - Detection parameters
     */
    sendDetectionRequest(params) {
        this.send({
            type: 'detect',
            payload: params
        });
    }

    /**
     * Send image detection request (original backend protocol)
     * @param {string} imageData - Base64 encoded image data
     */
    sendImageDetection(imageData) {
        this.send({
            type: 'image_detection',
            image: imageData
        });
    }

    /**
     * Send ping message for heartbeat
     */
    sendPing() {
        this.send({
            type: 'ping'
        });
    }

    /**
     * Send model change request
     * @param {string} modelId - Model ID
     */
    sendModelChange(modelId) {
        this.send({
            type: 'change_model',
            payload: { model: modelId }
        });
    }

    /**
     * Request system status
     */
    requestStatus() {
        this.send({
            type: 'get_status',
            payload: {}
        });
    }

    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.emit('maxReconnectAttemptsReached');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            this.connect();
        }, delay);
    }

    /**
     * Disconnect WebSocket
     */
    disconnect() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }
    }

    /**
     * Start heartbeat interval
     */
    startHeartbeat() {
        this.stopHeartbeat(); // Clear any existing interval
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected()) {
                this.sendPing();
            }
        }, this.heartbeatDelay);
    }

    /**
     * Stop heartbeat interval
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * Add event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * Remove event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * Emit event to listeners
     * @param {string} event - Event name
     * @param {*} data - Event data
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Get connection status
     * @returns {string} Connection status
     */
    getStatus() {
        if (!this.ws) return 'disconnected';
        
        switch (this.ws.readyState) {
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return 'connected';
            case WebSocket.CLOSING:
                return 'closing';
            case WebSocket.CLOSED:
                return 'disconnected';
            default:
                return 'unknown';
        }
    }

    /**
     * Check if WebSocket is connected
     * @returns {boolean} Connection status
     */
    isConnected() {
        return this.ws && this.ws.readyState === WebSocket.OPEN;
    }
}

// Create global WebSocket service instance
window.wsService = new WebSocketService();

// Setup WebSocket event listeners for UI updates
window.wsService.on('connected', () => {
    console.log('WebSocket connected to server');
    window.toast?.success('连接成功', 'WebSocket 连接已建立');
});

window.wsService.on('disconnected', () => {
    console.log('WebSocket disconnected from server');
    window.toast?.warning('连接断开', 'WebSocket 连接已断开');
});

window.wsService.on('detectionProgress', (data) => {
    console.log('Detection progress:', data);
    // Update progress UI if needed
});

window.wsService.on('detectionComplete', (data) => {
    console.log('Detection complete:', data);
    if (window.controlPanel) {
        window.controlPanel.onInferenceComplete(data);
    }
});

window.wsService.on('detectionError', (data) => {
    console.error('Detection error:', data);
    window.toast?.error('检测失败', data.message || '检测过程中发生错误');
});

window.wsService.on('systemStatus', (data) => {
    console.log('System status:', data);
    // Update system status UI
    const systemInfo = document.getElementById('system-info');
    if (systemInfo) {
        systemInfo.textContent = data.gpu_enabled ? 'GPU 已启用' : 'GPU 未启用';
    }
});

// Original backend protocol event listeners
window.wsService.on('detectionStarted', (data) => {
    console.log('Detection started:', data);
    window.toast?.info('检测开始', data.message || '开始处理图像...');

    // Update control panel state
    if (window.controlPanel) {
        window.controlPanel.setProcessingState(true);
    }

    // Show loading on image display
    if (window.imageDisplay) {
        window.imageDisplay.showLoading(true);
    }
});

window.wsService.on('detectionResult', (data) => {
    console.log('Detection result:', data);

    // Update control panel state
    if (window.controlPanel) {
        window.controlPanel.setProcessingState(false);
    }

    // Hide loading on image display
    if (window.imageDisplay) {
        window.imageDisplay.hideLoading();
    }

    if (data.success) {
        // Handle successful detection
        window.toast?.success('检测完成', data.message || '检测成功完成');

        // Convert backend detection format to frontend format
        const convertedDetections = data.detections.map((detection, index) => {
            const [x1, y1, x2, y2] = detection.bbox;
            return {
                id: index + 1,
                class: detection.class_name,
                classKey: detection.class_name.toLowerCase().replace(/\s+/g, '_'),
                confidence: detection.confidence,
                bbox: [x1, y1, x2 - x1, y2 - y1], // Convert to [x, y, width, height]
                area: (x2 - x1) * (y2 - y1)
            };
        });

        // Update results
        if (window.controlPanel) {
            window.controlPanel.onInferenceComplete({
                detections: convertedDetections,
                processingTime: 1000, // Placeholder
                model: 'yolo-v8',
                parameters: {
                    confidence: 0.5,
                    nms: 0.4
                }
            });
        }

        // Update image display with detections
        if (window.imageDisplay && convertedDetections.length > 0) {
            window.imageDisplay.updateDetections(convertedDetections, data.image_size);
        }
    } else {
        // Handle detection failure
        window.toast?.error('检测失败', data.message || data.error || '检测过程中发生错误');
    }
});

window.wsService.on('error', (data) => {
    console.error('WebSocket error:', data);
    window.toast?.error('服务器错误', data.message || '服务器处理时发生错误');

    // Reset UI state
    if (window.controlPanel) {
        window.controlPanel.setProcessingState(false);
    }

    if (window.imageDisplay) {
        window.imageDisplay.hideLoading();
    }
});

window.wsService.on('pong', () => {
    console.log('Received pong from server');
    // Heartbeat response - no UI update needed
});
