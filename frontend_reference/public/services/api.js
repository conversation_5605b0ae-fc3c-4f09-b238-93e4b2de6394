/**
 * API Service
 * Handles communication with backend API
 */
class ApiService {
    constructor() {
        this.baseUrl = this.getBaseUrl();
        this.timeout = 30000; // 30 seconds
    }

    /**
     * Get base URL for API
     * @returns {string} Base URL
     */
    getBaseUrl() {
        // In production, this would be configured based on environment
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        const port = window.location.port;
        
        // Default to same origin with API path
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return `${protocol}//${hostname}:8000/api`; // Assuming backend runs on port 8000
        }
        
        return `${protocol}//${hostname}${port ? ':' + port : ''}/api`;
    }

    /**
     * Make HTTP request
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {Promise<Object>} Response data
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        
        const config = {
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            
            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            
            return await response.text();
            
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            throw error;
        }
    }

    /**
     * Upload image for detection
     * @param {File|Blob} imageFile - Image file
     * @param {Object} options - Detection options
     * @returns {Promise<Object>} Detection results
     */
    async uploadImage(imageFile, options = {}) {
        const formData = new FormData();
        formData.append('image', imageFile);
        formData.append('model', options.model || 'yolo-v8');
        formData.append('confidence', options.confidence || 0.5);
        formData.append('nms', options.nms || 0.4);

        return this.request('/detect', {
            method: 'POST',
            body: formData,
            headers: {} // Remove Content-Type to let browser set it with boundary
        });
    }

    /**
     * Detect objects from image URL
     * @param {string} imageUrl - Image URL
     * @param {Object} options - Detection options
     * @returns {Promise<Object>} Detection results
     */
    async detectFromUrl(imageUrl, options = {}) {
        return this.request('/detect-url', {
            method: 'POST',
            body: JSON.stringify({
                url: imageUrl,
                model: options.model || 'yolo-v8',
                confidence: options.confidence || 0.5,
                nms: options.nms || 0.4
            })
        });
    }

    /**
     * Get available models
     * @returns {Promise<Array>} List of available models
     */
    async getModels() {
        return this.request('/models');
    }

    /**
     * Get model information
     * @param {string} modelId - Model ID
     * @returns {Promise<Object>} Model information
     */
    async getModelInfo(modelId) {
        return this.request(`/models/${modelId}`);
    }

    /**
     * Get system status
     * @returns {Promise<Object>} System status
     */
    async getStatus() {
        return this.request('/status');
    }

    /**
     * Health check
     * @returns {Promise<Object>} Health status
     */
    async healthCheck() {
        return this.request('/health');
    }

    /**
     * Get detection history
     * @param {Object} params - Query parameters
     * @returns {Promise<Object>} Detection history
     */
    async getHistory(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/history?${queryString}` : '/history';
        return this.request(endpoint);
    }

    /**
     * Delete detection result
     * @param {string} resultId - Result ID
     * @returns {Promise<Object>} Delete response
     */
    async deleteResult(resultId) {
        return this.request(`/results/${resultId}`, {
            method: 'DELETE'
        });
    }

    /**
     * Save detection result
     * @param {Object} result - Detection result
     * @returns {Promise<Object>} Save response
     */
    async saveResult(result) {
        return this.request('/results', {
            method: 'POST',
            body: JSON.stringify(result)
        });
    }

    /**
     * Mock API for development/demo
     * @param {Object} params - Request parameters
     * @returns {Promise<Object>} Mock response
     */
    async mockDetection(params) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
        
        // Generate mock detections based on confidence threshold
        const mockDetections = [
            { id: 1, class: '汽车', confidence: 0.92, bbox: [100, 150, 200, 120], area: 24000 },
            { id: 2, class: '行人', confidence: 0.87, bbox: [300, 200, 80, 180], area: 14400 },
            { id: 3, class: '交通灯', confidence: 0.75, bbox: [450, 80, 60, 100], area: 6000 },
            { id: 4, class: '汽车', confidence: 0.82, bbox: [150, 300, 180, 100], area: 18000 },
            { id: 5, class: '自行车', confidence: 0.68, bbox: [50, 250, 120, 80], area: 9600 },
            { id: 6, class: '行人', confidence: 0.91, bbox: [380, 180, 70, 190], area: 13300 }
        ];

        // Filter by confidence threshold
        const filteredDetections = mockDetections.filter(
            d => d.confidence >= (params.confidence || 0.5)
        );

        return {
            success: true,
            detections: filteredDetections,
            processingTime: Math.random() * 1000 + 500,
            model: params.model || 'yolo-v8',
            parameters: {
                confidence: params.confidence || 0.5,
                nms: params.nms || 0.4
            },
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Check if API is available
     * @returns {Promise<boolean>} API availability
     */
    async isApiAvailable() {
        try {
            await this.healthCheck();
            return true;
        } catch (error) {
            console.warn('API not available, using mock mode:', error.message);
            return false;
        }
    }
}

// Create global API service instance
window.apiService = new ApiService();
