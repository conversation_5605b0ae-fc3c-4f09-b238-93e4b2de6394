<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>托盘状态检测系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/layout.css">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header id="header" class="header">
            <div class="header-content">
                <div class="header-left">
                    <button id="menu-btn" class="icon-btn">
                        <span class="icon">☰</span>
                    </button>
                    <h1 class="app-title">托盘状态检测系统</h1>
                </div>
                <div class="header-right">
                    <!-- Connection Status Indicator -->
                    <div id="connection-status" class="connection-status connecting">
                        <span class="status-dot"></span>
                        <span class="status-text">连接中...</span>
                    </div>
                    <button id="settings-btn" class="icon-btn">
                        <span class="icon">⚙️</span>
                    </button>
                    <button id="help-btn" class="icon-btn">
                        <span class="icon">❓</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Side - Image Display -->
            <div class="image-section">
                <div id="image-display" class="image-display">
                    <div id="image-container" class="image-container">
                        <div id="placeholder" class="image-placeholder">
                            <span class="placeholder-text">点击加载图像开始检测</span>
                        </div>
                        <img id="main-image" class="main-image" style="display: none;" alt="待检测图像">
                        <canvas id="detection-canvas" class="detection-canvas" style="display: none;"></canvas>
                        <div id="loading-overlay" class="loading-overlay" style="display: none;">
                            <div class="spinner"></div>
                            <span>处理中...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Controls & Results -->
            <div class="control-section">
                <!-- Model Panel -->
                <div class="panel model-panel">
                    <h3 class="panel-title">模型选择</h3>
                    <select id="model-select" class="model-select">
                        <option value="yolo-v8">YOLO v8</option>
                        <option value="faster-rcnn">Faster R-CNN</option>
                        <option value="ssd">SSD MobileNet</option>
                        <option value="efficientdet">EfficientDet</option>
                    </select>
                </div>

                <!-- Tabs -->
                <div class="tabs-container">
                    <div class="tabs-header">
                        <button class="tab-btn active" data-tab="controls">控制面板</button>
                        <button class="tab-btn" data-tab="results">检测结果</button>
                    </div>

                    <!-- Control Panel Tab -->
                    <div id="controls-tab" class="tab-content active">
                        <div class="panel control-panel">
                            <!-- Main Action Buttons -->
                            <div class="control-actions">
                                <button id="load-image-btn" class="action-btn load-btn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-up" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M12 12v6"></path><path d="m15 15-3-3-3 3"></path></svg>
                                    <span>Load Image</span>
                                </button>
                                <button id="run-inference-btn" class="action-btn run-btn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-play" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><polygon points="10 8 16 12 10 16 10 8"></polygon></svg>
                                    <span>Run Inference</span>
                                </button>
                                <input type="file" id="file-input" accept="image/*" style="display: none;">
                            </div>

                            <div class="control-divider"></div>

                            <!-- Confidence Threshold -->
                            <div class="control-group slider-group">
                                <div class="slider-header">
                                    <label class="control-label">Confidence Threshold</label>
                                    <span id="confidence-value" class="slider-value">65%</span>
                                </div>
                                <div class="slider-container">
                                    <input type="range" id="confidence-slider" min="0" max="1" step="0.01" value="0.65" class="modern-slider">
                                </div>
                            </div>

                            <!-- Real-time Processing Toggle -->
                            <div class="control-group toggle-group">
                                <label class="control-label">Real-time Processing</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="realtime-toggle" class="toggle-input">
                                    <label for="realtime-toggle" class="toggle-label"></label>
                                </div>
                            </div>

                            <div class="control-divider"></div>

                            <!-- Detection Classes -->
                            <div class="control-group classes-group">
                                <h4 class="section-title">Detection Classes</h4>
                                <div class="classes-grid">
                                    <div class="class-item">
                                        <input type="checkbox" id="class-pallet" class="class-checkbox" checked>
                                        <label for="class-pallet" class="class-label">
                                            <span class="class-dot class-dot-green"></span>
                                            <span>空托盘</span>
                                        </label>
                                    </div>
                                    <div class="class-item">
                                        <input type="checkbox" id="class-non-empty-pallet" class="class-checkbox" checked>
                                        <label for="class-non-empty-pallet" class="class-label">
                                            <span class="class-dot class-dot-yellow"></span>
                                            <span>有物品托盘</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="control-divider"></div>

                            <!-- Bottom Action Buttons -->
                            <div class="control-actions">
                                <button id="save-results-btn" class="action-btn save-btn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-save" aria-hidden="true"><path d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"></path><path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"></path><path d="M7 3v4a1 1 0 0 0 1 1h7"></path></svg>
                                    <span>Save Results</span>
                                </button>
                                <button id="settings-btn-panel" class="action-btn settings-btn">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings" aria-hidden="true"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                                    <span>Settings</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Results Panel Tab -->
                    <div id="results-tab" class="tab-content">
                        <div class="panel results-panel">
                            <!-- Results Header -->
                            <div class="results-header">
                                <h3 class="results-title">Detection Results</h3>
                                <div class="results-actions">
                                    <button id="download-results-btn" class="results-action-btn" title="下载结果">
                                        <span class="action-icon">⬇️</span>
                                    </button>
                                    <button id="share-results-btn" class="results-action-btn" title="分享结果">
                                        <span class="action-icon">🔗</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Statistics Cards -->
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-label">Objects Detected</div>
                                    <div id="objects-count" class="stat-value">4</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Processing Time</div>
                                    <div id="processing-time-display" class="stat-value">1500 ms</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Classes</div>
                                    <div id="classes-count" class="stat-value">3</div>
                                </div>
                            </div>

                            <!-- Results Table -->
                            <div id="results-table-container" class="results-table-container">
                                <table id="results-table" class="results-table">
                                    <thead>
                                        <tr>
                                            <th class="col-class">Class</th>
                                            <th class="col-confidence">Confidence</th>
                                            <th class="col-position">Position</th>
                                        </tr>
                                    </thead>
                                    <tbody id="results-table-body">
                                        <tr class="no-results-row">
                                            <td colspan="3" class="no-results-cell">暂无检测结果</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer id="footer" class="footer">
            <div class="footer-content">
                <div class="status-section">
                    <span class="status-label">状态:</span>
                    <span id="status-text" class="status-text ready">就绪</span>
                </div>
                <div class="system-info">
                    <span id="system-info" class="system-text">GPU 未启用</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script src="services/api.js"></script>
    <script src="services/websocket.js"></script>
    <script src="components/ImageDisplay.js"></script>
    <script src="components/ControlPanel.js"></script>
    <script src="components/ResultsPanel.js"></script>
    <script src="components/ConnectionStatus.js"></script>
    <script src="components/Toast.js"></script>
    <script src="App.js"></script>
    <script src="index.js"></script>
</body>
</html>
