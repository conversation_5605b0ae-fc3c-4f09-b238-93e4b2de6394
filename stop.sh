#!/bin/bash

# YOLO目标检测系统停止脚本

echo "🛑 停止YOLO目标检测系统..."

# 读取PID文件
if [ -f ".backend_pid" ]; then
    BACKEND_PID=$(cat .backend_pid)
    if ps -p $BACKEND_PID > /dev/null 2>&1; then
        echo "🔧 停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        sleep 2
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            echo "⚠️  强制停止后端服务..."
            kill -9 $BACKEND_PID
        fi
        echo "✅ 后端服务已停止"
    else
        echo "ℹ️  后端服务未运行"
    fi
    rm -f .backend_pid
else
    echo "ℹ️  未找到后端PID文件"
fi

if [ -f ".frontend_pid" ]; then
    FRONTEND_PID=$(cat .frontend_pid)
    if ps -p $FRONTEND_PID > /dev/null 2>&1; then
        echo "🌐 停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        sleep 2
        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            echo "⚠️  强制停止前端服务..."
            kill -9 $FRONTEND_PID
        fi
        echo "✅ 前端服务已停止"
    else
        echo "ℹ️  前端服务未运行"
    fi
    rm -f .frontend_pid
else
    echo "ℹ️  未找到前端PID文件"
fi

# 额外检查并清理可能的残留进程
echo "🧹 清理残留进程..."

# 查找并停止可能的Python HTTP服务器进程
pkill -f "python.*http.server.*8080" 2>/dev/null
pkill -f "python.*app.py" 2>/dev/null

echo "✅ 系统已完全停止"
echo ""
echo "💡 要重新启动系统，请运行: ./start.sh" 