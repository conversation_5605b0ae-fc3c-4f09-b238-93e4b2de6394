#!/bin/bash

# 网络配置检测脚本

echo "🔍 YOLO目标检测系统 - 网络配置检测"
echo "================================"

# 检查本机IP地址
echo "📡 检测网络接口..."
LOCAL_IP=$(ifconfig | grep -E "inet (10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)" | grep -v 127.0.0.1 | awk '{print $2}' | head -1)

if [ -z "$LOCAL_IP" ]; then
    LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
fi

if [ -z "$LOCAL_IP" ]; then
    echo "❌ 无法获取本机IP地址"
    echo "请检查网络连接"
    exit 1
else
    echo "✅ 本机IP地址: $LOCAL_IP"
fi

# 检测网络接口详情
echo ""
echo "🌐 网络接口详情:"
ifconfig | grep -A 1 "flags=.*UP.*" | grep -E "(^[a-z]|inet )" | grep -v "127.0.0.1" | head -10

# 检查端口占用情况
echo ""
echo "🔌 检测端口占用情况..."

# 检查8000端口
if lsof -i :8000 >/dev/null 2>&1; then
    echo "✅ 端口8000已被占用 (后端服务)"
    lsof -i :8000 | head -2
else
    echo "❌ 端口8000未被占用 (后端服务未启动)"
fi

# 检查8080端口
if lsof -i :8080 >/dev/null 2>&1; then
    echo "✅ 端口8080已被占用 (前端服务)"
    lsof -i :8080 | head -2
else
    echo "❌ 端口8080未被占用 (前端服务未启动)"
fi

# 测试本机连接
echo ""
echo "🧪 测试本机连接..."

if curl -s -I "http://localhost:8080" >/dev/null 2>&1; then
    echo "✅ 本机前端服务连接正常"
else
    echo "❌ 本机前端服务连接失败"
fi

if curl -s -I "http://localhost:8000/health" >/dev/null 2>&1; then
    echo "✅ 本机后端服务连接正常"
else
    echo "❌ 本机后端服务连接失败"
fi

# 测试局域网连接
echo ""
echo "🌐 测试局域网连接..."

if curl -s -I "http://$LOCAL_IP:8080" >/dev/null 2>&1; then
    echo "✅ 局域网前端服务连接正常"
else
    echo "❌ 局域网前端服务连接失败"
    echo "   可能的原因："
    echo "   • 防火墙阻止了8080端口"
    echo "   • 服务未绑定到0.0.0.0"
fi

if curl -s -I "http://$LOCAL_IP:8000/health" >/dev/null 2>&1; then
    echo "✅ 局域网后端服务连接正常"
else
    echo "❌ 局域网后端服务连接失败"
    echo "   可能的原因："
    echo "   • 防火墙阻止了8000端口"
    echo "   • 服务未绑定到0.0.0.0"
fi

# 防火墙检测 (macOS)
echo ""
echo "🛡️  防火墙状态检测..."
if command -v pfctl >/dev/null 2>&1; then
    if sudo pfctl -s info 2>/dev/null | grep -q "Status: Enabled"; then
        echo "⚠️  macOS防火墙已启用"
        echo "   如果无法访问，请在系统偏好设置中允许Python访问网络"
    else
        echo "✅ macOS防火墙未启用"
    fi
else
    echo "ℹ️  无法检测防火墙状态"
fi

# 生成访问链接
echo ""
echo "🔗 访问链接："
echo "================================"
echo "📱 本机访问:"
echo "   http://localhost:8080"
echo ""
echo "🌐 局域网访问:"
echo "   http://$LOCAL_IP:8080"
echo ""
echo "📊 API文档:"
echo "   http://$LOCAL_IP:8000/docs"

# 生成简单的二维码（如果有qrencode工具）
if command -v qrencode >/dev/null 2>&1; then
    echo ""
    echo "📱 二维码访问 (扫描访问):"
    qrencode -t ANSI "http://$LOCAL_IP:8080"
    echo ""
    echo "💡 安装二维码工具: brew install qrencode"
fi

echo ""
echo "✅ 网络检测完成！"
echo ""
echo "💡 故障排除建议:"
echo "  1. 如果局域网无法访问，尝试关闭防火墙"
echo "  2. 确保所有设备在同一WiFi网络"
echo "  3. 尝试重启路由器"
echo "  4. 检查是否有网络隔离设置" 