# Pallet Monitor - 视觉目标检测系统

这是一个基于B/S架构的视觉目标检测系统，支持智能的conda环境管理和一键部署。

## 工程结构

```
pallet_monitor/
├── backend/                      # 后端服务
│   ├── api/                      # RESTful API 模块
│   ├── core_engine/              # 核心检测引擎
│   │   └── weights/              # 存放YOLO模型权重文件 (.pt)
│   ├── websocket/                # WebSocket服务模块
│   ├── static/                   # 存放静态文件，如检测结果图片
│   ├── __init__.py
│   ├── app.py                    # 后端应用主入口 (例如使用FastAPI或Flask)
│   └── requirements.txt          # Python依赖项
│
├── frontend/                     # 前端应用
│   ├── public/                   # 存放公共文件，如 index.html
│   ├── src/                      # 前端源码
│   │   ├── assets/               # 存放图片、图标等静态资源
│   │   ├── components/           # UI组件 (如视频播放器、控制面板)
│   │   ├── services/             # 用于和后端API、WebSocket通信的模块
│   │   ├── styles/               # CSS样式文件
│   │   ├── App.js                # 主应用组件 (以React为例)
│   │   └── index.js              # 前端应用入口
│   └── package.json              # 前端依赖和项目脚本
│
├── start.sh                      # 一键启动脚本 (支持conda环境)
├── stop.sh                       # 停止服务脚本
├── demo.sh                       # 演示脚本
├── setup_env.sh                  # 环境配置脚本
├── PROJECT_OVERVIEW.md           # 项目详细概览
├── start_system.md               # 系统启动指南
├── .gitignore                    # Git忽略文件配置
└── README.md                     # 项目说明文档
```

## 🚀 快速开始

### 环境配置（推荐使用conda）

```bash
# 方法1: 一键启动（自动检测并配置conda环境）
./start.sh

# 方法2: 专门配置conda环境
./setup_env.sh

# 方法3: 演示系统功能
./demo.sh
```

### 手动配置conda环境

```bash
# 创建虚拟环境
conda create -n pallet_vision python=3.11 -c conda-forge --override-channels -y
conda activate pallet_vision

# 安装依赖
cd backend
pip install -r requirements.txt

# 启动服务
python app.py
```

### 访问系统

- 前端界面: http://localhost:8080
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 规划说明

1.  **`backend/`**: 所有服务器端的代码都存放在这里。
    *   **`core_engine/`**: 专门负责目标检测的核心逻辑，与Web服务解耦，方便单独测试和升级。`weights/` 子目录用来存放您训练或下载的YOLO模型文件。
    *   **`api/`**: 存放处理HTTP请求的API代码，例如用于获取历史数据、管理摄像头等。
    *   **`websocket/`**: 专门处理实时通信逻辑，比如接收前端发来的视频流，并将检测结果实时推送到前端。
    *   **`app.py`**: 整个后端服务的启动入口，它会整合`api`和`websocket`的功能，并启动Web服务。
    *   **`requirements.txt`**: 明确列出后端服务所需的所有Python库（如`fastapi`, `uvicorn`, `python-socketio`, `ultralytics`等），便于环境部署。

2.  **`frontend/`**: 所有用户界面相关的代码都存放在这里。
    *   采用现代前端框架（如React, Vue, Svelte等）的典型目录结构。
    *   **`src/components/`**: 将UI拆分为可复用的组件，提高代码的可维护性。
    *   **`src/services/`**: 将数据请求的逻辑（无论是API还是WebSocket）从UI组件中分离出来，使代码更清晰。
