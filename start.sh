#!/bin/bash

# 托盘状态检测系统启动脚本
# 使用新的前端架构 (frontend_reference) 和原有后端

echo "🚀 启动托盘状态检测系统..."
echo "================================"

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "⚠️  警告: conda 未安装，使用系统Python环境"
    USE_CONDA=false
else
    echo "✅ 检测到conda环境"
    USE_CONDA=true
fi

# Python环境配置
if [ "$USE_CONDA" = true ]; then
    echo "🐍 配置conda环境..."
    
    # 初始化conda（如果需要）
    eval "$(conda shell.bash hook)"
    
    # 检查pallet_vision环境是否存在
    if conda env list | grep -q "pallet_vision"; then
        echo "✅ 检测到现有的 pallet_vision 环境"
        echo "🔄 激活 pallet_vision 环境..."
        conda activate pallet_vision
    else
        echo "📦 创建新的 pallet_vision conda环境..."
        # 使用conda-forge避免Terms of Service问题，禁用默认channels
        conda create -n pallet_vision python=3.11 -c conda-forge --override-channels -y
        
        if [ $? -ne 0 ]; then
            echo "❌ 错误: 创建conda环境失败"
            echo ""
            echo "💡 可能的解决方案:"
            echo "1. 手动接受Terms of Service:"
            echo "   conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/main"
            echo "   conda tos accept --override-channels --channel https://repo.anaconda.com/pkgs/r"
            echo ""
            echo "2. 或者手动创建环境:"
            echo "   conda create -n pallet_vision python=3.11 -c conda-forge --override-channels -y"
            echo ""
            echo "3. 或者使用系统Python环境:"
            echo "   cd backend && pip install -r requirements.txt && python app.py"
            exit 1
        fi
        
        echo "🔄 激活 pallet_vision 环境..."
        conda activate pallet_vision
    fi
    
    echo "✅ conda环境配置完成"
    PYTHON_CMD="python"
    PIP_CMD="pip"
else
    # 使用系统Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ 错误: Python3 未安装"
        echo "请先安装Python3: https://www.python.org/downloads/"
        exit 1
    fi
    
    if ! command -v pip &> /dev/null && ! command -v pip3 &> /dev/null; then
        echo "❌ 错误: pip 未安装"
        echo "请先安装pip"
        exit 1
    fi
    
    PYTHON_CMD="python3"
    if command -v pip3 &> /dev/null; then
        PIP_CMD="pip3"
    else
        PIP_CMD="pip"
    fi
fi

# 检查requirements.txt是否存在
if [ ! -f "backend/requirements.txt" ]; then
    echo "❌ 错误: backend/requirements.txt 不存在"
    exit 1
fi

echo "📦 安装Python依赖包..."
cd backend

# 安装依赖
$PIP_CMD install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ 错误: 安装后端依赖失败"
    exit 1
fi

echo "✅ 后端依赖安装完成"

# 检查YOLO模型文件是否存在
if [ ! -f "core_engine/weights/yolov8n.pt" ]; then
    echo "⚠️  警告: YOLO模型文件不存在，首次运行时会自动下载"
fi

echo ""
echo "🔧 启动后端服务..."
echo "后端地址: http://localhost:8000"
echo "WebSocket: ws://localhost:8000/ws"
echo "API文档: http://localhost:8000/docs"
echo ""

# 在后台启动后端服务
nohup $PYTHON_CMD app.py > ../backend.log 2>&1 &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 检查后端是否成功启动
if ps -p $BACKEND_PID > /dev/null; then
    echo "✅ 后端服务启动成功 (PID: $BACKEND_PID)"
else
    echo "❌ 后端服务启动失败，请查看 backend.log"
    exit 1
fi

# 返回项目根目录
cd ..

echo ""
echo "🌐 启动前端服务..."
echo "前端地址: http://localhost:8080"
echo ""

# 检查新前端架构文件是否存在
if [ ! -f "frontend_reference/public/index.html" ]; then
    echo "❌ 错误: 新前端架构文件不存在"
    kill $BACKEND_PID
    exit 1
fi

cd frontend_reference

# 确保 public 目录中有最新的文件
echo "📁 同步文件到 public 目录..."
mkdir -p public/styles public/services public/components

# 复制样式文件
cp src/styles/* public/styles/ 2>/dev/null || true

# 复制服务文件
cp src/services/* public/services/ 2>/dev/null || true

# 复制组件文件
cp src/components/* public/components/ 2>/dev/null || true

# 复制主要 JS 文件
cp src/App.js public/ 2>/dev/null || true
cp src/index.js public/ 2>/dev/null || true

echo "✅ 文件同步完成"

# 在后台启动前端服务（从public目录启动，绑定到所有网络接口）
nohup python3 -m http.server 8080 --directory public --bind 0.0.0.0 > ../frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端启动
sleep 2

# 检查前端是否成功启动
if ps -p $FRONTEND_PID > /dev/null; then
    echo "✅ 前端服务启动成功 (PID: $FRONTEND_PID)"
else
    echo "❌ 前端服务启动失败，请查看 frontend.log"
    kill $BACKEND_PID
    exit 1
fi

cd ..

# 获取本机IP地址
LOCAL_IP=$(ifconfig | grep -E "inet (10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)" | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
if [ -z "$LOCAL_IP" ]; then
    # 备用方法获取IP
    LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "未获取到IP")
fi

echo ""
echo "🎉 系统启动成功！"
echo "================================"
echo "📱 本机访问地址: http://localhost:8080"
echo "🌐 局域网访问地址: http://${LOCAL_IP}:8080"
echo "🔧 后端API地址: http://${LOCAL_IP}:8000"
echo "📚 API文档地址: http://${LOCAL_IP}:8000/docs"
echo ""
echo "进程信息:"
echo "  - 后端PID: $BACKEND_PID"
echo "  - 前端PID: $FRONTEND_PID"
echo ""
echo "日志文件:"
echo "  - 后端日志: backend.log"
echo "  - 前端日志: frontend.log"
echo ""
echo "💡 使用说明:"
echo "  📱 本机访问: 打开浏览器访问 http://localhost:8080"
echo "  🌐 局域网访问: 其他设备打开浏览器访问 http://${LOCAL_IP}:8080"
echo "  🔗 等待WebSocket连接成功（右上角绿色状态）"
echo "  📷 点击'加载图像'选择图片进行检测"
echo "  🎯 点击'运行检测'开始AI检测"
echo "  🖱️  点击检测框或结果列表进行交互"
echo ""
echo "📋 局域网访问注意事项:"
echo "  • 确保防火墙允许8000和8080端口访问"
echo "  • 所有设备需连接在同一局域网内"
echo "  • 移动设备可直接扫描二维码访问（如需要）"
echo ""
echo "🛑 停止服务:"
echo "  kill $BACKEND_PID $FRONTEND_PID"
echo ""

# 保存PID到文件以便停止
echo "$BACKEND_PID" > .backend_pid
echo "$FRONTEND_PID" > .frontend_pid

echo "按Ctrl+C或运行 ./stop.sh 来停止服务"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; rm -f .backend_pid .frontend_pid; echo "✅ 服务已停止"; exit 0' INT

# 保持脚本运行
while true; do
    sleep 1
done 