#!/usr/bin/env python
"""
YOLO检测功能测试脚本
使用现有的bus.jpg图片测试检测功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core_engine.detector import YOLODetector
import json

def test_detection():
    """测试YOLO检测功能"""
    print("🔬 YOLO检测功能测试")
    print("=" * 40)
    
    # 显示Python环境信息
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 工作目录: {os.getcwd()}")
    print()
    
    # 初始化检测器
    print("📦 初始化YOLO检测器...")
    try:
        detector = YOLODetector()
        print("✅ YOLO模型加载成功")
    except Exception as e:
        print(f"❌ YOLO模型加载失败: {e}")
        return False
    
    # 测试图片路径
    test_image_path = os.path.join(os.path.dirname(__file__), "core_engine", "bus.jpg")
    
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图片不存在: {test_image_path}")
        return False
    
    print(f"🖼️  使用测试图片: {test_image_path}")
    
    # 执行检测
    print("🔍 开始检测...")
    try:
        result = detector.detect_from_image_path(test_image_path)
        
        if result['success']:
            detections = result['detections']
            image_size = result['image_size']
            
            print("✅ 检测成功!")
            print(f"📏 图片尺寸: {image_size[0]} x {image_size[1]}")
            print(f"🎯 检测到 {len(detections)} 个目标:")
            print("-" * 40)
            
            for i, detection in enumerate(detections, 1):
                bbox = detection['bbox']
                class_name = detection['class_name']
                confidence = detection['confidence']
                
                print(f"{i}. {class_name}")
                print(f"   置信度: {confidence:.3f} ({confidence*100:.1f}%)")
                print(f"   位置: ({bbox[0]:.1f}, {bbox[1]:.1f}) -> ({bbox[2]:.1f}, {bbox[3]:.1f})")
                print(f"   尺寸: {bbox[2]-bbox[0]:.1f} x {bbox[3]-bbox[1]:.1f}")
                print()
            
            # 保存检测结果到JSON文件
            output_file = "test_detection_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"💾 检测结果已保存到: {output_file}")
            
        else:
            print(f"❌ 检测失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 检测过程中发生错误: {e}")
        return False
    
    print("\n🎉 测试完成!")
    return True

if __name__ == "__main__":
    success = test_detection()
    sys.exit(0 if success else 1) 