from fastapi import FastAP<PERSON>, WebSocket
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import logging
import sys
import os

# 添加当前目录到Python路径，以便导入模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from websocket.handler import websocket_endpoint

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="视觉目标检测系统",
    description="基于WebSocket的实时目标检测系统",
    version="1.0.0"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件目录
static_dir = os.path.join(os.path.dirname(__file__), "static")
if not os.path.exists(static_dir):
    os.makedirs(static_dir)

app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "视觉目标检测系统API",
        "version": "1.0.0",
        "websocket_url": "/ws",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "message": "服务运行正常"}

@app.websocket("/ws")
async def websocket_handler(websocket: WebSocket):
    """WebSocket端点"""
    await websocket_endpoint(websocket)

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("=== YOLO目标检测系统启动 ===")
    logger.info("正在初始化YOLO模型...")
    # 这里YOLO模型会在第一次WebSocket连接时初始化
    logger.info("系统启动完成")
    logger.info("WebSocket端点: ws://localhost:8000/ws")
    logger.info("API文档: http://localhost:8000/docs")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("=== YOLO目标检测系统关闭 ===")

if __name__ == "__main__":
    import uvicorn
    
    logger.info("启动开发服务器...")
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 