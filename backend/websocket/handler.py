import asyncio
import json
import logging
from fastapi import WebSocket, WebSocketDisconnect
from core_engine.detector import YOLODetector

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []
        self.detector = YOLODetector()
        
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket连接已建立，当前连接数: {len(self.active_connections)}")
        
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")
        
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """向特定WebSocket连接发送消息"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            
    async def broadcast(self, message: dict):
        """向所有连接的客户端广播消息"""
        for connection in self.active_connections.copy():
            try:
                await connection.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                await self.disconnect(connection)
    
    async def handle_message(self, websocket: WebSocket, data: str):
        """处理接收到的WebSocket消息"""
        try:
            message = json.loads(data)
            message_type = message.get('type')
            
            if message_type == 'image_detection':
                await self.handle_image_detection(websocket, message)
            elif message_type == 'ping':
                await self.send_personal_message({'type': 'pong'}, websocket)
            else:
                logger.warning(f"未知消息类型: {message_type}")
                
        except json.JSONDecodeError:
            logger.error("无法解析JSON消息")
            await self.send_personal_message({
                'type': 'error',
                'message': '无效的JSON格式'
            }, websocket)
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
            await self.send_personal_message({
                'type': 'error',
                'message': str(e)
            }, websocket)
    
    async def handle_image_detection(self, websocket: WebSocket, message: dict):
        """处理图像检测请求"""
        try:
            # 发送处理开始通知
            await self.send_personal_message({
                'type': 'detection_started',
                'message': '开始处理图像...'
            }, websocket)
            
            # 获取base64图像数据
            image_data = message.get('image')
            if not image_data:
                await self.send_personal_message({
                    'type': 'error',
                    'message': '缺少图像数据'
                }, websocket)
                return
            
            # 执行YOLO检测
            logger.info("开始YOLO检测...")
            detection_result = self.detector.detect_from_base64(image_data)
            
            if detection_result['success']:
                logger.info(f"检测完成，发现 {len(detection_result['detections'])} 个目标")
                
                # 发送检测结果
                await self.send_personal_message({
                    'type': 'detection_result',
                    'success': True,
                    'detections': detection_result['detections'],
                    'image_size': detection_result['image_size'],
                    'message': f"检测完成！发现 {len(detection_result['detections'])} 个目标"
                }, websocket)
            else:
                logger.error(f"检测失败: {detection_result.get('error', '未知错误')}")
                await self.send_personal_message({
                    'type': 'detection_result',
                    'success': False,
                    'error': detection_result.get('error', '检测失败'),
                    'message': '检测失败，请重试'
                }, websocket)
                
        except Exception as e:
            logger.error(f"处理图像检测时发生错误: {e}")
            await self.send_personal_message({
                'type': 'error',
                'message': f'处理图像时发生错误: {str(e)}'
            }, websocket)

# 全局WebSocket管理器实例
manager = WebSocketManager()

async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点处理函数"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            await manager.handle_message(websocket, data)
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("客户端断开连接")
    except Exception as e:
        logger.error(f"WebSocket连接出现异常: {e}")
        manager.disconnect(websocket) 