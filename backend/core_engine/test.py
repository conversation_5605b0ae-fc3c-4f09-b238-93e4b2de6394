
import torch
from ultralytics import YOLO
import os

def run_test():
    """
    Tests the YOLOv8 installation by running a prediction on a sample image.
    """
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    # Load a pretrained YOLOv8n model
    # The model will be downloaded automatically on the first run
    print("Loading pretrained YOLOv8n model...")
    model = YOLO('yolov8n.pt')
    print("Model loaded successfully.")

    # Define the image URL for prediction
    image_url = 'https://ultralytics.com/images/bus.jpg'

    # Run prediction
    print(f"Running prediction on: {image_url}")
    # Run prediction and save the result
    # The `save=True` argument tells ultralytics to save the output image
    print(f"Running prediction on: {image_url}")
    results = model(image_url, save=True)
    print("Prediction complete.")

    # The result image is saved in the 'runs/detect/predict' directory by default
    print(f"Result image saved in '{os.path.abspath('runs/detect/predict')}' directory.")

    # Process results
    for result in results:
        # Access the Boxes object for detection results
        boxes = result.boxes
        print(f"Found {len(boxes)} objects.")

        # Print details for each detected object
        for i, box in enumerate(boxes):
            class_id = int(box.cls)
            class_name = model.names[class_id]
            confidence = float(box.conf)
            print(f"  - Object {i+1}: {class_name} (Confidence: {confidence:.2f})")


if __name__ == "__main__":
    run_test()
