import torch
from ultralytics import YOLO
import cv2
import numpy as np
import base64
from PIL import Image
import io
import os

class YOLODetector:
    def __init__(self, model_path="weights/yolov8n.pt"):
        """
        初始化YOLO检测器
        
        Args:
            model_path: YOLO模型权重文件路径
        """
        self.model_path = os.path.join(os.path.dirname(__file__), model_path)
        print(f"Loading YOLO model from: {self.model_path}")
        
        # 解决PyTorch 2.6版本的weights_only安全检查问题
        # 方法1: 添加ultralytics相关类到安全全局列表
        try:
            import ultralytics.nn.tasks
            torch.serialization.add_safe_globals([
                ultralytics.nn.tasks.DetectionModel,
                ultralytics.nn.tasks.SegmentationModel,
                ultralytics.nn.tasks.ClassificationModel,
                ultralytics.nn.tasks.PoseModel
            ])
            print("Added ultralytics classes to safe globals")
        except (ImportError, AttributeError) as e:
            print(f"Warning: Could not add ultralytics classes to safe globals: {e}")
        
        # 方法2: 使用猴子补丁临时修改torch.load的默认weights_only参数
        original_torch_load = torch.load
        def patched_torch_load(f, map_location=None, pickle_module=None, weights_only=None, **kwargs):
            # 如果weights_only未指定，设置为False以兼容旧版本行为
            if weights_only is None:
                weights_only = False
            return original_torch_load(f, map_location=map_location, pickle_module=pickle_module, weights_only=weights_only, **kwargs)
        
        # 临时替换torch.load
        torch.load = patched_torch_load
        
        try:
            self.model = YOLO(self.model_path)
            print("YOLO model loaded successfully")
        finally:
            # 恢复原始的torch.load函数
            torch.load = original_torch_load
        
    def detect_from_base64(self, base64_image):
        """
        从base64编码的图像进行目标检测
        
        Args:
            base64_image: base64编码的图像字符串
            
        Returns:
            dict: 包含检测结果的字典
        """
        try:
            # 解码base64图像
            image_data = base64.b64decode(base64_image.split(',')[1])  # 去掉data:image/...;base64,前缀
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为RGB格式（YOLO需要RGB格式）
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 运行YOLO检测
            results = self.model(image)
            
            # 处理检测结果
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # 获取边界框坐标
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        
                        # 获取类别和置信度
                        class_id = int(box.cls.cpu().numpy())
                        confidence = float(box.conf.cpu().numpy())
                        class_name = self.model.names[class_id]
                        
                        detection = {
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'class_id': class_id,
                            'class_name': class_name,
                            'confidence': confidence
                        }
                        detections.append(detection)
            
            return {
                'success': True,
                'detections': detections,
                'image_size': [image.width, image.height]
            }
            
        except Exception as e:
            print(f"Detection error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'detections': []
            }
    
    def detect_from_image_path(self, image_path):
        """
        从图像文件路径进行目标检测
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            dict: 包含检测结果的字典
        """
        try:
            # 运行YOLO检测
            results = self.model(image_path)
            
            # 处理检测结果
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # 获取边界框坐标
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        
                        # 获取类别和置信度
                        class_id = int(box.cls.cpu().numpy())
                        confidence = float(box.conf.cpu().numpy())
                        class_name = self.model.names[class_id]
                        
                        detection = {
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'class_id': class_id,
                            'class_name': class_name,
                            'confidence': confidence
                        }
                        detections.append(detection)
            
            # 获取图像尺寸
            image = Image.open(image_path)
            
            return {
                'success': True,
                'detections': detections,
                'image_size': [image.width, image.height]
            }
            
        except Exception as e:
            print(f"Detection error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'detections': []
            } 