class YOLODetectionApp {
    constructor() {
        this.websocket = null;
        this.currentImage = null;
        this.currentImageData = null;
        this.detectionResults = [];
        this.isConnected = false;
        
        // 元素引用
        this.elements = {
            connectionStatus: document.getElementById('connectionStatus'),
            uploadArea: document.getElementById('uploadArea'),
            imageInput: document.getElementById('imageInput'),
            displaySection: document.getElementById('displaySection'),
            imageCanvas: document.getElementById('imageCanvas'),
            imageInfo: document.getElementById('imageInfo'),
            detectBtn: document.getElementById('detectBtn'),
            clearBtn: document.getElementById('clearBtn'),
            resultsSection: document.getElementById('resultsSection'),
            resultsStats: document.getElementById('resultsStats'),
            resultsList: document.getElementById('resultsList'),
            statusMessages: document.getElementById('statusMessages'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            loadingText: document.getElementById('loadingText')
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.connectWebSocket();
        this.addStatusMessage('系统初始化完成', 'info');
    }
    
    setupEventListeners() {
        // 图像上传事件
        this.elements.uploadArea.addEventListener('click', () => {
            this.elements.imageInput.click();
        });
        
        this.elements.imageInput.addEventListener('change', (e) => {
            this.handleImageSelect(e.target.files[0]);
        });
        
        // 拖拽上传事件
        this.elements.uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.add('dragover');
        });
        
        this.elements.uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.remove('dragover');
        });
        
        this.elements.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleImageSelect(files[0]);
            }
        });
        
        // 按钮事件
        this.elements.detectBtn.addEventListener('click', () => {
            this.startDetection();
        });
        
        this.elements.clearBtn.addEventListener('click', () => {
            this.clearImage();
        });
        
        // 画布点击事件（显示检测框）
        this.elements.imageCanvas.addEventListener('click', (e) => {
            this.handleCanvasClick(e);
        });
    }
    
    connectWebSocket() {
        // 动态获取WebSocket地址，支持局域网访问
        const hostname = window.location.hostname;
        const wsUrl = `ws://${hostname}:8000/ws`;
        this.addStatusMessage(`正在连接WebSocket服务器 (${hostname}:8000)...`, 'info');
        
        try {
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                this.isConnected = true;
                this.updateConnectionStatus('connected', '已连接到服务器');
                this.addStatusMessage('WebSocket连接成功', 'success');
            };
            
            this.websocket.onmessage = (event) => {
                this.handleWebSocketMessage(JSON.parse(event.data));
            };
            
            this.websocket.onclose = () => {
                this.isConnected = false;
                this.updateConnectionStatus('disconnected', '连接已断开');
                this.addStatusMessage('WebSocket连接断开', 'warning');
                
                // 自动重连
                setTimeout(() => {
                    this.addStatusMessage('尝试重新连接...', 'info');
                    this.connectWebSocket();
                }, 3000);
            };
            
            this.websocket.onerror = (error) => {
                this.addStatusMessage('WebSocket连接错误', 'error');
                console.error('WebSocket error:', error);
            };
            
        } catch (error) {
            this.addStatusMessage('无法连接到服务器', 'error');
            console.error('WebSocket connection error:', error);
        }
    }
    
    updateConnectionStatus(status, text) {
        this.elements.connectionStatus.className = `connection-status ${status}`;
        this.elements.connectionStatus.querySelector('.status-text').textContent = text;
    }
    
    handleImageSelect(file) {
        if (!file) return;
        
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.addStatusMessage('请选择有效的图像文件', 'error');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            this.loadImage(e.target.result);
        };
        reader.readAsDataURL(file);
        
        this.addStatusMessage(`已选择图片: ${file.name}`, 'info');
    }
    
    loadImage(dataUrl) {
        const img = new Image();
        img.onload = () => {
            this.currentImage = img;
            this.currentImageData = dataUrl;
            this.displayImage();
            this.clearResults();
        };
        img.src = dataUrl;
    }
    
    displayImage() {
        const canvas = this.elements.imageCanvas;
        const ctx = canvas.getContext('2d');
        
        // 设置画布尺寸
        const maxWidth = 800;
        const maxHeight = 600;
        
        let { width, height } = this.currentImage;
        
        // 按比例缩放
        if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // 绘制图像
        ctx.drawImage(this.currentImage, 0, 0, width, height);
        
        // 显示图像信息
        this.elements.imageInfo.textContent = 
            `尺寸: ${this.currentImage.width} × ${this.currentImage.height} | 显示尺寸: ${width} × ${height}`;
        
        // 显示相关区域
        this.elements.displaySection.style.display = 'block';
        
        this.addStatusMessage('图片加载完成，可以开始检测', 'success');
    }
    
    startDetection() {
        if (!this.isConnected) {
            this.addStatusMessage('请等待WebSocket连接', 'warning');
            return;
        }
        
        if (!this.currentImageData) {
            this.addStatusMessage('请先选择图片', 'warning');
            return;
        }
        
        this.showLoading('正在发送图片到服务器...');
        this.elements.detectBtn.disabled = true;
        
        // 发送检测请求
        const message = {
            type: 'image_detection',
            image: this.currentImageData
        };
        
        this.websocket.send(JSON.stringify(message));
        this.addStatusMessage('已发送图片，等待检测结果...', 'info');
    }
    
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'detection_started':
                this.updateLoading('YOLO模型正在处理图片...');
                this.addStatusMessage(message.message, 'info');
                break;
                
            case 'detection_result':
                this.hideLoading();
                this.elements.detectBtn.disabled = false;
                
                if (message.success) {
                    this.displayDetectionResults(message.detections, message.image_size);
                    this.addStatusMessage(message.message, 'success');
                } else {
                    this.addStatusMessage(message.message || '检测失败', 'error');
                }
                break;
                
            case 'error':
                this.hideLoading();
                this.elements.detectBtn.disabled = false;
                this.addStatusMessage(message.message, 'error');
                break;
                
            case 'pong':
                // 心跳响应
                break;
                
            default:
                console.log('Unknown message type:', message.type);
        }
    }
    
    displayDetectionResults(detections, imageSize) {
        this.detectionResults = detections;
        
        // 显示统计信息
        this.elements.resultsStats.textContent = 
            `检测到 ${detections.length} 个目标物体`;
        
        // 清空结果列表
        this.elements.resultsList.innerHTML = '';
        
        // 显示检测结果列表
        detections.forEach((detection, index) => {
            const resultItem = this.createResultItem(detection, index);
            this.elements.resultsList.appendChild(resultItem);
        });
        
        // 在画布上绘制检测框
        this.drawDetectionBoxes(detections, imageSize);
        
        // 显示结果区域
        this.elements.resultsSection.style.display = 'block';
    }
    
    createResultItem(detection, index) {
        const item = document.createElement('div');
        item.className = 'result-item';
        
        // 获取该类别对应的颜色
        const classColor = this.getColorForClass(detection.class_id);
        
        // 设置边框颜色和背景色
        item.style.borderLeftColor = classColor;
        item.style.backgroundColor = classColor + '15'; // 添加透明度
        
        item.innerHTML = `
            <div class="result-info">
                <div class="result-class" style="color: ${classColor}; font-weight: bold;">${detection.class_name}</div>
                <div class="result-confidence">置信度: ${(detection.confidence * 100).toFixed(1)}%</div>
                <div class="result-bbox">
                    位置: (${Math.round(detection.bbox[0])}, ${Math.round(detection.bbox[1])}) - 
                    (${Math.round(detection.bbox[2])}, ${Math.round(detection.bbox[3])})
                </div>
            </div>
            <div class="result-color-indicator" style="background-color: ${classColor};"></div>
        `;
        
        // 点击高亮检测框
        item.addEventListener('click', () => {
            this.highlightDetection(index);
        });
        
        return item;
    }
    
    drawDetectionBoxes(detections, originalSize) {
        const canvas = this.elements.imageCanvas;
        const ctx = canvas.getContext('2d');
        
        // 重新绘制原图
        ctx.drawImage(this.currentImage, 0, 0, canvas.width, canvas.height);
        
        // 计算缩放比例
        const scaleX = canvas.width / originalSize[0];
        const scaleY = canvas.height / originalSize[1];
        
        // 绘制检测框
        detections.forEach((detection, index) => {
            const [x1, y1, x2, y2] = detection.bbox;
            
            // 转换坐标
            const scaledX1 = x1 * scaleX;
            const scaledY1 = y1 * scaleY;
            const scaledX2 = x2 * scaleX;
            const scaledY2 = y2 * scaleY;
            
            // 设置样式
            ctx.strokeStyle = this.getColorForClass(detection.class_id);
            ctx.lineWidth = 3;
            ctx.fillStyle = this.getColorForClass(detection.class_id) + '20';
            
            // 绘制矩形框
            ctx.fillRect(scaledX1, scaledY1, scaledX2 - scaledX1, scaledY2 - scaledY1);
            ctx.strokeRect(scaledX1, scaledY1, scaledX2 - scaledX1, scaledY2 - scaledY1);
            
            // 绘制标签
            const label = `${detection.class_name} (${(detection.confidence * 100).toFixed(1)}%)`;
            const labelY = scaledY1 > 30 ? scaledY1 - 10 : scaledY1 + 20;
            
            ctx.fillStyle = this.getColorForClass(detection.class_id);
            ctx.fillRect(scaledX1, labelY - 15, ctx.measureText(label).width + 10, 20);
            
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText(label, scaledX1 + 5, labelY);
        });
    }
    
    getColorForClass(classId) {
        const colors = [
            '#FF6B6B',  // 珊瑚红 - person
            '#4ECDC4',  // 青绿色 - bicycle
            '#45B7D1',  // 天蓝色 - car
            '#FF9F43',  // 橙色 - motorcycle
            '#6C5CE7',  // 紫色 - airplane
            '#A29BFE',  // 淡紫色 - bus
            '#FD79A8',  // 粉色 - train
            '#00B894',  // 深绿色 - truck
            '#FDCB6E',  // 金黄色 - boat
            '#E17055',  // 棕橙色 - traffic light
            '#74B9FF',  // 亮蓝色 - fire hydrant
            '#81ECEC',  // 浅蓝绿 - stop sign
            '#55A3FF',  // 蓝色 - parking meter
            '#FF7675',  // 淡红色 - bench
            '#00CECA'   // 青色 - bird
        ];
        return colors[classId % colors.length];
    }
    
    highlightDetection(index) {
        // 移除其他高亮
        document.querySelectorAll('.result-item').forEach(item => {
            item.classList.remove('highlighted');
        });
        
        // 高亮选中的项
        const items = document.querySelectorAll('.result-item');
        if (items[index]) {
            items[index].classList.add('highlighted');
        }
        
        // 在画布上高亮显示对应的检测框
        this.drawHighlightedBox(index);
    }
    
    drawHighlightedBox(index) {
        if (!this.detectionResults[index]) return;
        
        const canvas = this.elements.imageCanvas;
        const ctx = canvas.getContext('2d');
        
        // 重新绘制所有检测框
        this.drawDetectionBoxes(this.detectionResults, [this.currentImage.width, this.currentImage.height]);
        
        // 高亮显示选中的检测框
        const detection = this.detectionResults[index];
        const [x1, y1, x2, y2] = detection.bbox;
        
        const scaleX = canvas.width / this.currentImage.width;
        const scaleY = canvas.height / this.currentImage.height;
        
        const scaledX1 = x1 * scaleX;
        const scaledY1 = y1 * scaleY;
        const scaledX2 = x2 * scaleX;
        const scaledY2 = y2 * scaleY;
        
        // 绘制高亮框
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 5;
        ctx.setLineDash([5, 5]);
        ctx.strokeRect(scaledX1 - 3, scaledY1 - 3, (scaledX2 - scaledX1) + 6, (scaledY2 - scaledY1) + 6);
        ctx.setLineDash([]);
    }
    
    handleCanvasClick(e) {
        if (!this.detectionResults.length) return;
        
        const canvas = this.elements.imageCanvas;
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // 查找点击的检测框
        for (let i = 0; i < this.detectionResults.length; i++) {
            const detection = this.detectionResults[i];
            const [x1, y1, x2, y2] = detection.bbox;
            
            const scaleX = canvas.width / this.currentImage.width;
            const scaleY = canvas.height / this.currentImage.height;
            
            const scaledX1 = x1 * scaleX;
            const scaledY1 = y1 * scaleY;
            const scaledX2 = x2 * scaleX;
            const scaledY2 = y2 * scaleY;
            
            if (x >= scaledX1 && x <= scaledX2 && y >= scaledY1 && y <= scaledY2) {
                this.highlightDetection(i);
                break;
            }
        }
    }
    
    clearImage() {
        this.currentImage = null;
        this.currentImageData = null;
        this.clearResults();
        
        this.elements.displaySection.style.display = 'none';
        this.elements.imageInput.value = '';
        
        this.addStatusMessage('已清除图片', 'info');
    }
    
    clearResults() {
        this.detectionResults = [];
        this.elements.resultsSection.style.display = 'none';
        this.elements.resultsList.innerHTML = '';
        this.elements.resultsStats.textContent = '';
    }
    
    showLoading(text) {
        this.elements.loadingText.textContent = text;
        this.elements.loadingOverlay.style.display = 'flex';
    }
    
    updateLoading(text) {
        this.elements.loadingText.textContent = text;
    }
    
    hideLoading() {
        this.elements.loadingOverlay.style.display = 'none';
    }
    
    addStatusMessage(message, type = 'info') {
        const messageElement = document.createElement('div');
        messageElement.className = `status-message ${type}`;
        messageElement.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
        
        this.elements.statusMessages.appendChild(messageElement);
        
        // 自动滚动到最新消息
        this.elements.statusMessages.scrollTop = this.elements.statusMessages.scrollHeight;
        
        // 限制消息数量
        const messages = this.elements.statusMessages.querySelectorAll('.status-message');
        if (messages.length > 10) {
            messages[0].remove();
        }
        
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    // 心跳检测
    startHeartbeat() {
        setInterval(() => {
            if (this.isConnected && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000); // 每30秒发送一次心跳
    }
}

// 添加高亮样式
const style = document.createElement('style');
style.textContent = `
    .result-item.highlighted {
        background: rgba(255, 215, 0, 0.3) !important;
        border-left-color: #FFD700 !important;
        transform: translateX(10px) !important;
        box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
    }
`;
document.head.appendChild(style);

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    const app = new YOLODetectionApp();
    app.startHeartbeat();
    
    // 全局错误处理
    window.addEventListener('error', (e) => {
        console.error('Global error:', e.error);
        app.addStatusMessage('发生未知错误', 'error');
    });
    
    console.log('YOLO检测应用初始化完成');
}); 