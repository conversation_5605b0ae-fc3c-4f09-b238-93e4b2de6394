/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

/* 容器样式 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header p {
    font-size: 1rem;
    color: #666;
}

/* 连接状态指示器 */
.connection-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.95);
    padding: 10px 20px;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ffc107;
    animation: pulse 2s infinite;
}

.connection-status.connected .status-dot {
    background: #28a745;
    animation: none;
}

.connection-status.disconnected .status-dot {
    background: #dc3545;
    animation: none;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

/* 主体双列布局 */
.main {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    min-height: 0;
}

/* 左列：图像操作区 */
.left-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 右列：结果和状态区 */
.right-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 上传区域样式 - 更紧凑 */
.upload-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.upload-area {
    padding: 1.5rem;
    text-align: center;
    border: 2px dashed #ddd;
    margin: 15px;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    opacity: 0.7;
}

.upload-content p {
    font-size: 1rem;
    margin-bottom: 0.25rem;
    color: #333;
}

.upload-hint {
    font-size: 0.85rem !important;
    color: #666 !important;
}

/* 显示区域样式 */
.display-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.image-container {
    position: relative;
    text-align: center;
    margin-bottom: 1rem;
    flex: 1;
}

#imageCanvas {
    max-width: 100%;
    max-height: 400px;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.15);
    cursor: crosshair;
}

.image-info {
    margin-top: 0.75rem;
    font-size: 0.8rem;
    color: #666;
}

/* 控制按钮样式 */
.controls {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 10px 20px;
    border: none;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 3px 10px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 3px 10px rgba(108, 117, 125, 0.4);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.6);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-icon {
    font-size: 1rem;
}

/* 结果区域样式 */
.results-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.results-section h3 {
    margin-bottom: 1rem;
    color: #333;
    text-align: center;
    font-size: 1.3rem;
}

.results-stats {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 0.75rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    text-align: center;
    font-weight: 500;
    font-size: 0.9rem;
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

.results-list {
    display: grid;
    gap: 0.75rem;
    max-height: 350px;
    overflow-y: auto;
    flex: 1;
}

.result-item {
    background: rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    padding: 0.75rem;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.result-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.result-info {
    flex: 1;
}

.result-color-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-left: 10px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    flex-shrink: 0;
}

.result-class {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.result-confidence {
    color: #666;
    font-size: 0.85rem;
    margin-top: 0.15rem;
}

.result-bbox {
    font-size: 0.75rem;
    color: #999;
    margin-top: 0.15rem;
}

/* 状态消息样式 */
.status-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem;
    max-height: 200px;
    display: flex;
    flex-direction: column;
}

.status-messages {
    flex: 1;
    overflow-y: auto;
}

.status-message {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.4rem;
    border-radius: 6px;
    animation: slideIn 0.3s ease;
    font-size: 0.85rem;
}

.status-message:last-child {
    margin-bottom: 0;
}

.status-message.info {
    background: rgba(23, 162, 184, 0.1);
    border-left: 3px solid #17a2b8;
    color: #0c5460;
}

.status-message.success {
    background: rgba(40, 167, 69, 0.1);
    border-left: 3px solid #28a745;
    color: #155724;
}

.status-message.error {
    background: rgba(220, 53, 69, 0.1);
    border-left: 3px solid #dc3545;
    color: #721c24;
}

.status-message.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left: 3px solid #ffc107;
    color: #856404;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载遮罩层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loadingText {
    font-size: 1rem;
    font-weight: 500;
}

/* 底部样式 */
.footer {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin-top: 1.5rem;
}

.footer p {
    color: #666;
    font-size: 0.85rem;
}

/* 高亮样式 */
.result-item.highlighted {
    transform: translateX(10px) !important;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4) !important;
    border: 2px solid #FFD700 !important;
    border-left-width: 4px !important;
}

.result-item.highlighted .result-color-indicator {
    border-color: #FFD700 !important;
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.6) !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .left-column,
    .right-column {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .upload-area {
        padding: 1rem;
        margin: 10px;
        min-height: 100px;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
    
    .display-section,
    .results-section,
    .status-section {
        padding: 1rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    
    #imageCanvas {
        max-height: 300px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
} 