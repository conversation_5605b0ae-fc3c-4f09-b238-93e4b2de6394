{"name": "yolo-detection-frontend", "version": "1.0.0", "description": "YOLO目标检测系统前端", "main": "src/app.js", "scripts": {"start": "python -m http.server 8080 --directory public", "dev": "python3 -m http.server 8080 --directory public", "serve": "npx http-server public -p 8080 -c-1 --cors", "build": "echo 'No build process required for vanilla JS'", "lint": "echo 'No linting configured'", "test": "echo 'No tests configured'"}, "keywords": ["yolo", "object-detection", "websocket", "computer-vision", "frontend"], "author": "YOLO Detection Team", "license": "MIT", "dependencies": {}, "devDependencies": {"http-server": "^14.1.1"}, "repository": {"type": "git", "url": "."}, "engines": {"node": ">=14.0.0"}}