<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习视觉目标检测系统</title>
    <link rel="stylesheet" href="src/styles/style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎯 深度学习视觉目标检测系统</h1>
            <p>选择图片，实时检测目标物体</p>
        </header>

        <!-- 连接状态指示器 -->
        <div class="connection-status" id="connectionStatus">
            <span class="status-dot"></span>
            <span class="status-text">连接中...</span>
        </div>

        <main class="main">
            <!-- 左列：图像操作区域 -->
            <div class="left-column">
                <!-- 图像上传区域 -->
                <div class="upload-section">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <div class="upload-icon">📷</div>
                            <p>点击选择图片或拖拽图片到此处</p>
                            <p class="upload-hint">支持 JPG、PNG、JPEG 格式</p>
                        </div>
                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                    </div>
                </div>

                <!-- 图像显示和检测结果区域 -->
                <div class="display-section" id="displaySection" style="display: none;">
                    <div class="image-container">
                        <canvas id="imageCanvas"></canvas>
                        <div class="image-info">
                            <span id="imageInfo">图像信息</span>
                        </div>
                    </div>

                    <div class="controls">
                        <button id="detectBtn" class="btn btn-primary">
                            <span class="btn-icon">🔍</span>
                            开始检测
                        </button>
                        <button id="clearBtn" class="btn btn-secondary">
                            <span class="btn-icon">🗑️</span>
                            清除
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右列：结果和状态区域 -->
            <div class="right-column">
                <!-- 检测结果显示区域 -->
                <div class="results-section" id="resultsSection" style="display: none;">
                    <h3>检测结果</h3>
                    <div class="results-stats" id="resultsStats"></div>
                    <div class="results-list" id="resultsList"></div>
                </div>

                <!-- 状态消息区域 -->
                <div class="status-section">
                    <div class="status-messages" id="statusMessages"></div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>© 2025 视觉目标检测系统 - 基于深度学习模型 </p>
        </footer>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p id="loadingText">处理中...</p>
        </div>
    </div>

    <script src="src/app.js"></script>
</body>
</html> 