#!/bin/bash

# YOLO目标检测系统演示脚本

echo "🎬 YOLO目标检测系统演示"
echo "================================"
echo ""

# 显示项目信息
echo "📁 项目结构预览:"
echo "backend/         # 后端服务 (FastAPI + WebSocket + YOLO)"
echo "frontend/        # 前端应用 (HTML/CSS/JavaScript)"
echo "start.sh         # 一键启动脚本"
echo "stop.sh          # 停止服务脚本"
echo ""

# 检查环境
echo "🔍 检查运行环境..."

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "⚠️  conda 未安装，使用系统Python环境"
    USE_CONDA=false
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装"
        exit 1
    fi
    PYTHON_CMD="python3"
    echo "✅ Python3: $(python3 --version)"
else
    echo "✅ 检测到conda环境"
    USE_CONDA=true
    
    # 初始化conda
    eval "$(conda shell.bash hook)"
    
    # 检查并激活pallet_vision环境
    if conda env list | grep -q "pallet_vision"; then
        echo "✅ 激活 pallet_vision 环境"
        conda activate pallet_vision
        PYTHON_CMD="python"
    else
        echo "⚠️  未找到 pallet_vision 环境，使用系统Python"
        if ! command -v python3 &> /dev/null; then
            echo "❌ Python3 未安装"
            exit 1
        fi
        PYTHON_CMD="python3"
    fi
    echo "✅ Python: $(python --version 2>&1 || python3 --version)"
fi

if [ -f "backend/core_engine/weights/yolov8n.pt" ]; then
    echo "✅ YOLO模型: yolov8n.pt 已存在"
else
    echo "⚠️  YOLO模型: 首次运行时会自动下载"
fi

echo ""

# 演示检测功能
echo "🧪 演示检测功能 (使用测试图片)..."
echo "正在测试YOLO检测器..."
echo ""

cd backend

# 运行检测测试
if $PYTHON_CMD test_detection.py; then
    echo ""
    echo "✅ 检测功能正常！"
    
    # 显示检测结果
    if [ -f "test_detection_result.json" ]; then
        echo ""
        echo "📊 检测结果摘要:"
        $PYTHON_CMD -c "
import json
with open('test_detection_result.json', 'r') as f:
    result = json.load(f)
    
if result['success']:
    detections = result['detections']
    print(f'🎯 检测到 {len(detections)} 个目标:')
    for i, det in enumerate(detections, 1):
        print(f'  {i}. {det[\"class_name\"]} (置信度: {det[\"confidence\"]:.3f})')
else:
    print('❌ 检测失败')
"
    fi
else
    echo "❌ 检测测试失败，请检查依赖安装"
fi

cd ..
echo ""

# 提供启动选项
echo "🚀 启动选项:"
echo "1. 一键启动完整系统: ./start.sh"
echo "2. 手动启动后端: cd backend && python app.py"
echo "3. 手动启动前端: cd frontend && python -m http.server 8080 --directory public"
echo ""

echo "🌐 访问地址:"
echo "- 前端界面: http://localhost:8080"
echo "- 后端API: http://localhost:8000"
echo "- API文档: http://localhost:8000/docs"
echo ""

echo "📋 使用流程:"
echo "1. 启动系统"
echo "2. 打开浏览器访问 http://localhost:8080"
echo "3. 等待WebSocket连接成功（绿色状态）"
echo "4. 选择或拖拽图片到上传区域"
echo "5. 点击'开始检测'按钮"
echo "6. 查看检测结果和可视化边界框"
echo ""

# 询问是否启动
echo "💡 提示: 查看完整文档请阅读 PROJECT_OVERVIEW.md"
echo ""
read -p "是否现在启动系统? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ""
    echo "🚀 启动系统..."
    ./start.sh
else
    echo ""
    echo "👋 演示完成！"
    echo "需要启动时请运行: ./start.sh"
fi 